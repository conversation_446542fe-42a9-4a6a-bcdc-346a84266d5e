package main

import (
	"flag"
	_ "pay-center/docs"
	"pay-center/routers"
	"time"

	"github.com/maybgit/glog"

	"github.com/labstack/echo/v4"
	echoSwagger "github.com/swaggo/echo-swagger"
)

// @title pay_echo项目接口文档
// @version 1.0
// @description 这里是描述
// @host localhost:7035
func main() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh
    glog.Info(123)
	e := echo.New()

	//日志命令行参数化处理，可以启用禁用控制台日志等，defer确认在程序退出时将所有缓冲日志写入es
	defer glog.Flush()
	flag.Parse()
	//定时任务启动

	glog.Info("pay-center test")

	e.GET("/swagger/*", echoSwagger.WrapHandler)

	e = routers.InitRouter(e)

	// echo 启动
	e.Logger.Fatal(e.Start(":7035"))
}
