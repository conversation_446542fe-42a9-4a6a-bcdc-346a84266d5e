package dto

//统一订单请求参数
type PayInfo struct {
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付 10:APP微信支付 11：app支付宝支付
	TransType int32 `json:"transType" validate:"required,lt=20,gt=0"`
	//商户流水号
	OutTradeNo string `json:"outTradeNo" validate:"required,max=32"`
	//交易金额
	PayPrice int32 `json:"payPrice" validate:"required,gt=0"`
	//总金额
	TotalPrice int32 `json:"totalPrice" validate:"required,gt=0"`
	//优惠金额
	Discount  int32  `json:"discount"`
	ProductId string `json:"productId"   validate:"required,max=32"`
	//商品名称
	ProductName string `json:"productName"  validate:"required"`
	//商品描述
	ProductDesc string `json:"productDesc"  validate:"required"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl" validate:"url"`
	ClientIP         string `json:"clientIP"  validate:"required,ip"`
	//微信 JSAPI 支付时必传
	Openid string `json:"openid" `
	//子商户公众账号 ID
	SubAppId string `json:"subAppId"   validate:"required"`
	//商户号
	MerchantId string `json:"merchantId"   validate:"required"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo"`
	//扩展信息 预留字段，JSON 格式
	OrderId string `json:"orderId" validate:"required"`
	//订单有限时间（分钟）
	ValidTime int32 `json:"validTime"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"orderPayType"`
	//appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
	AppId int32 `json:"appId"`
}

// @Desc				支付订单查询DTO
// <AUTHOR>
// @Date		 		2020-06-24
type PayQuery struct {
	// 商户流水号
	TradeNo string `json:"tradeNo" validate:"required,max=32"`
	// 商户号
	MerchantId string `json:"merchantId"   validate:"required"`
	// 签名
	Sign string `json:"sign" validate:"required,max=32"`
}

// @Desc				标准终端绑定DTO
// <AUTHOR>
// @Date		 		2020-06-24
type StdBinding struct {
	//机构号
	OrgNumber string `json:"orgNumber"`
	//机具编号(自定义)
	Tsn string `json:"tsn"`
	//电银商户号
	DyMchNo string `json:"dyMchNo"`
	//机具来源
	//1 – 外部代理商(默认)
	//2 – 电银代理商
	//（当机具来源为“电银代理商”时, 外部终
	//端号、终端厂家、终端型号非必传）
	SnSource string `json:"snSource"`
	//外部终端号(自定义)
	DyTermNo string `json:"dyTermNo"`
	//终端厂家
	TermFactory string `json:"termFactory"`
	//终端型号
	TermModel string `json:"termModel"`
	//终端名称（门店名称）
	TermName string `json:"termName"`
	//终端地址（门店地址）
	TermAddress string `json:"termAddress"`
}

type Notify struct {
	//支付中心订单号
	TradeNo string `json:trade_no`
	//订单号
	OrderNo string `json:order_no`
	//支付时间
	PayTime string `json:pay_time`
	//支付状态 0成功 1失败
	PayStatus int32 `json:pay_status`
}
type DyPay struct {
	//订单号
	OrderNo string `json:"order_no" form:"order_no"`
	//交易码,P00：默认值，支持微信、支付宝、云闪付、电银支付等；CSU01：仅支持云闪付（老接口兼容性保留值）；
	Trancde string `json:"trancde" form:"trancde"`
	//付款码,用户用银联支付宝、微信生成的付款码
	BarCode string `json:"bar_code" form:"bar_code"`
	//实付金额,单位：分
	PayAmount int32 `json:"pay_amount" form:"pay_amount"`
	//订单名称
	OrderName string `json:"order_name" form:"order_name"`
	//支付方式 1：微信 2：支付宝 3: 银联
	PayType int32 `json:"pay_type" form:"pay_type"`
	//总金额,单位：分
	PayTotal int32 `json:"pay_total" form:"pay_total"`
	//优惠金额,单位：分
	Discount int32 `json:"discount" form:"discount"`
	//回调地址
	NotifyUrl string `json:"notify_url" form:"notify_url"`
	//传机具编号（tsn）
	TrmSn string `json:"trm_sn" form:"trm_sn"`
	//终端号，传标准终端绑定接口返回的dyTermNo
	TrmId string `json:"trm_id" form:"trm_id"`
	//商户号
	MerchantId string `json:"merchant_id" form:"merchantId"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	ChannelId int `json:"channel_id"`
	//签名
	Sign string `json:"sign"`
}

type MemberInfo struct {
	Mobile     string `json:"mobile"`
	ScrmUserId string `json:"scrm_user_id"`
}
