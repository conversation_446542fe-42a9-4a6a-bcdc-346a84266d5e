package service

import (
	"context"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"net/http"
	"pay-center/dto"
	"pay-center/pkg/microservice/common/config"
	"pay-center/proto/pay"
	"time"
)

// 数据库链接
func GetConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.pay_center")
	//mySqlStr := "root:Rp000000@(211.162.70.169:3630)/pay_center?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}
	engine.ShowSQL()
	err = engine.Ping()
	//if err != nil {
	//	golog.Fatal("mysql ping fail", err)
	//	panic(err)
	//}
	return engine
}

func ReturnJson(c echo.Context, code int32, message string, data ...interface{}) error {
	return c.JSON(http.StatusOK, dto.Return{
		Code:    code,
		Message: message,
		Data:    data,
	})
}

type BaseClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
}

type payCenterClient struct {
	BaseClient
	PayInfo   pay.PayInfoClient
	PayRefund pay.PayRefundClient
}

func GetpayInfoClient() *payCenterClient {
	var client payCenterClient
	var err error
	url := config.GetString("grpc.payCenter")
	if url == "" {
		url = "127.0.0.1:7036"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.PayInfo = pay.NewPayInfoClient(client.Conn)
		client.PayRefund = pay.NewPayRefundClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*50)
		return &client
	}

}
