package service

import (
	"encoding/json"
	"net/http"
	"pay-center/dto"
	refund "pay-center/proto/pay"
	"pay-center/util"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Desc				退款
// <AUTHOR>
// @Date		 		2020-06-03
type Refund struct {
	//service.BaseServer
}

// PayRefund
// @Summary 退款
// @Tags 支付
// @Accept json
// @Produce json
// @Param model body dto.RefundDto true " "
// @Success 200 {object} pay.PayRefundResponse
// @Router /pay/refund [POST]
func PayRefund(ctx echo.Context) error {
	request := new(dto.RefundDto)
	if err := ctx.Bind(request); err != nil {
		glog.Error("统一退款接口求参数错误 Refund", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "退款参数错误")
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(request); err != nil {
		glog.Error("统一退款接口求参数效验错误 Refund", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "退款参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayRefund.Refund(client.Ctx, &refund.PayRefundRequest{
		TradeNo:     request.TradeNo,     // 支付中心流水号
		RefundAmt:   request.RefundAmt,   // 退款金额
		CallbackUrl: request.CallbackUrl, // 后台回调地址
		BackParam:   request.BackParam,   // 商户私有域
		ExtendInfo:  request.ExtendInfo,  // 扩展信息
		MerchantNo:  request.MerchantId,  // 商户号
		ClientIP:    request.ClientIP,    // 客户端 IP
		Domain:      util.GetDomain(ctx), // 请求域名
		RefundId:    request.RefundId,    //商户退款订单号
		AppId:       request.AppId,       //商户退款订单号
	}); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusBadRequest, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Desc				退款查询
// <AUTHOR>
// @Date		 		2020-06-04
// @Param<ctx>      	Context
// @Return				请求返回的内容
func PayRefundQuery(ctx echo.Context) error {
	request := new(dto.RefundQuery)
	if err := ctx.Bind(request); err != nil {
		glog.Error("退款查询接口求参数错误 RefundQuery", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "退款查询参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(request); err != nil {
		glog.Error("退款查询接口求参数效验错误 RefundQuery", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "退款参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayRefund.PayRefundQuery(client.Ctx, &refund.PayRefundQueryRequest{
		MerchantNo:    request.MerchantId,    // 商户号
		TransactionNo: request.TransactionNo, // 支付中心流水号
		ClientIP:      request.ClientIP,      // 客户端 IP
		RefundId:      request.RefundId,      // 退款订单号
		RefundAmt:     request.RefundAmt,     // 退款金额
	}); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusBadRequest, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Desc				电银退款结果异步回调通知
// <AUTHOR>
// @Date		 		2020-06-05
// @Param<ctx>      	Context
// @Return				请求返回的内容
func RefundNotification(ctx echo.Context) error {
	requestMap := util.GetRequestParams(ctx)
	bytes, _ := json.Marshal(requestMap)
	glog.Info("电银退款结果异步回调请求参数 RefundNotification：", string(bytes))
	request := refund.RefundNoticeRequest{}
	json.Unmarshal(bytes, &request)
	// 服务器签名效验
	result := util.VerifySign(requestMap, request.ServerSign, request.ServerCert)
	if result != nil {
		glog.Error("电银退款结果异步回调验证签名失败 RefundNotification：", result.Error(), requestMap)
		resp := map[string]string{"result": "签名效验失败"}
		return ctx.JSON(http.StatusOK, resp)
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if _, err := client.PayRefund.PayRefundNotice(client.Ctx, &request); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		resp := map[string]string{"result": "success"}
		return ctx.JSON(http.StatusOK, resp)
	}
}

//
//// RefundStatus
//// @Summary 退款状态
//// @Tags 支付
//// @Accept json
//// @Produce json
//// @Param model body dto.PayBase true " "
//// @Success 200 {object} dto.PayStatusResponse
//// @Failure 400 {object} dto.PayStatusResponse
//// @Router /pay/status [POST]
//func RefundStatus(ctx echo.Context) error {
//	var out pay.PayStatusResponse
//	model := new(dto.PayBase)
//	if err := ctx.Bind(model); err != nil {
//		glog.Error("支付状态查询接口求参数错误PayClose", model, err.Error())
//		return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
//	}
//	//// 对数据进行效验
//	//if err := util.ValidatorCheck(model); err != nil {
//	//	glog.Error("支付状态查询接口求参数错误QueryPayStatus", model, err.Error())
//	//	return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
//	//}
//	return ctx.JSON(http.StatusOK, out)
//}
