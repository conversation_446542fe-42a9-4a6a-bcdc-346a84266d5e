package dto

import "pay-center/proto/pay"

// @Desc				退款DTO
// <AUTHOR>
// @Date		 		2020-06-04
type RefundDto struct {
	TradeNo     string `json:"tradeNo" validate:"required,max=32"`    // 支付中心流水号
	RefundAmt   int32  `json:"refundAmt" validate:"required,gt=0"`    // 退款金额
	CallbackUrl string `json:"callbackUrl" validate:"url"`            // 后台回调地址
	BackParam   string `json:"backParam"`                             // 商户私有域
	ExtendInfo  string `json:"extendInfo"`                            // 扩展信息
	MerchantId  string `json:"merchantId" validate:"required,max=32"` // 商户号
	ClientIP    string `json:"clientIP"  validate:"required,ip"`      // 客户端 IP
	Sign        string `json:"sign" validate:"required,max=32"`       // 签名
	RefundType  int32  `json:"refundType"`
	RefundId    string `json:"refundId"` //商户退款订单号
	AppId       int32  `json:"appId"`    //渠道appid
}

// @Desc				退款查询DTO
// <AUTHOR>
// @Date		 		2020-06-10
type RefundQuery struct {
	MerchantId    string `json:"merchantId" validate:"required,max=32"`    // 商户号
	TransactionNo string `json:"transactionNo" validate:"required,max=32"` // 支付中心流水号
	ClientIP      string `json:"clientIP"  validate:"required,ip"`         // 客户端 IP
	RefundId      string `json:"refundId" validate:"required,max=32"`      // 退款订单号
	RefundAmt     int32  `json:"refundAmt" validate:"required,gt=0"`       // 退款金额
	Sign          string `json:"sign" validate:"required,max=32"`          // 签名
}

// @Desc				退款电银返回结果DTO
// <AUTHOR>
// @Date		 		2020-06-08
type RefundResponseDto struct {
	ReturnCode       string `json:"returnCode"`       // 返回状态码
	ReturnMessage    string `json:"returnMessage"`    // 返回信息
	Charset          string `json:"charset"`          // 字符集：固定 00-GBK
	Version          string `json:"version"`          // 版本号：固定值：1.0
	ServerCert       string `json:"serverCert"`       // 服务器证书
	SignType         string `json:"signType"`         // 签名类型：RSA
	ServerSign       string `json:"serverSign"`       // 服务器签名
	Service          string `json:"service"`          // 接口类型：固定值：DYRefund
	TradeNo          string `json:"tradeNo"`          // 流水号：系统返回的交易流水号
	MerchantId       string `json:"merchantId"`       // 商户编号
	OrderId          string `json:"orderId"`          // 商户订单号：合作平台的订单号
	RefundId         string `json:"refundId"`         // 退款订单号
	RefundDate       string `json:"refundDate"`       // 退款时间：YYYYMMDD
	Status           string `json:"status"`           // 退款状态（S:退款成功、P:退款处理中、F:退款失败）
	FailReason       string `json:"failReason"`       // 退款失败原因
	RefundAmount     string `json:"refundAmount"`     // 交易金额：退款金额，以分为单位
	CertId           string `json:"certId"`           // 证书序列号：预留字段，服务端证书序列号
	OfflineNotifyUrl string `json:"offlineNotifyUrl"` // 后台回调地址
	ClientIP         string `json:"clientIP"`         // 客户端 IP
	BackParam        string `json:"backParam"`        // 商户私有域：交易返回时原样返回给商户网站，给商户备用
	ExtendInfo       string `json:"extendInfo"`       // 扩展信息 ：预留字段，JSON 格式

	RspCode     string `json:"rspCode"`     // 退款查询返回状态码
	RspMessage  string `json:"rspMessage"`  // 退款查询返回信息
	RefundState string `json:"refundState"` // 退款查询返回状态
}

// @Desc				退款结果异步通知DTO
// <AUTHOR>
// @Date		 		2020-06-05
type RefundNotification struct {
	Charset      string `json:"charset"`      // 字符集:固定值 00，代表 GBK
	Version      string `json:"version"`      // 版本号:固定值 1.0
	SignType     string `json:"signType"`     // 签名类型:固定值 RSA
	ServerCert   string `json:"serverCert"`   // 服务证书
	ServerSign   string `json:"serverSign"`   // 服务签名
	OrderId      string `json:"orderId"`      // 商户订单号
	RefundId     string `json:"refundId"`     // 退款订单号
	TradeNo      string `json:"tradeNo"`      // 流水号：电银生成的退款流水号
	MerchantId   string `json:"merchantId"`   // 商户号
	TransAmt     int32  `json:"transAmt"`     // 退款金额：退款金额，以分为单位
	RefundDate   string `json:"refundDate"`   // 退款时间：YYYYMMDD
	TransState   string `json:"transState"`   // 退款状态:S: 成功(失败不通知)
	BackParam    string `json:"backParam"`    // 商户私有域：原样返回给商户
	ExtendInfo   string `json:"extendInfo"`   // 扩展信息：预留字段，JSON 格式
	MerchantSign string `json:"merchantSign"` // 签名
	MerchantCert string `json:"merchantCert"` // 证书
}
type NewRefund struct {
	//商户订单号(原交易)
	MerOrderNo string `json:"mer_order_no"`
	//交易码：非银联时（微信，支付宝）：P02 银联：CSU03
	Trancde string `json:"trancde"`
	//退款金额  单位：分
	RefundAmount string `json:"refund_amount"`
	//退款备注
	RefundRemark string `json:"refund_remark"`
	//请求头信息
	HeadBase  *pay.ScanHeadBase `json:"head_base"`
	NotifyUrl string            `json:"notify_url"`
}
