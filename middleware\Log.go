package middleware

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"pay-center/util"
)

//日志
func SetLog(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			list = make(map[string]string)
		)
		list = util.GetRequestParams(c)

		glog.Info("请求参数:", list)
		return next(c)
	}
}

//日志
func SetBaiduLog(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			list = make(map[string]string)
		)
		list = util.GetRequestParams(c)

		glog.Info("百度小程序支付回调日志，请求路径：", c.Request().RequestURI, "，请求参数：", list)
		return next(c)
	}
}
