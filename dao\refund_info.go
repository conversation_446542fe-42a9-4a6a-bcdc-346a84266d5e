package dao

type RefundInfo struct {
	Id           int    `xorm:"not null pk autoincr INT(11)"`
	PayId        string `xorm:"not null default '''' comment('支付id') CHAR(32)"`
	RefundAmount int    `xorm:"not null default 0 comment('退款额') INT(11)"`
	Status       int    `xorm:"not null default 0 comment('退款状态 0：未退款 1：退款成功 2：退款处理中 3：退款失败') INT(11)"`
	TradeNo      string `xorm:"not null default '''' comment('退款订单号') VARCHAR(32)"`
	DyTradeNo    string `xorm:"not null default '''' comment('退款电银生成的流水号') VARCHAR(32)"`
	NotifyUrl    string `xorm:"not null default '''' comment('后台回调地址') VARCHAR(200)"`
	ClientIp     string `xorm:"default '''' comment('客户端 IP') VARCHAR(30)"`
	AddTime      int    `xorm:"not null default 0 comment('添加时间') INT(11)"`
	RefundTime   int    `xorm:"not null default 0 comment('退款时间') INT(11)"`
	PushNum      int    `xorm:"not null default 0 comment('推送次数') INT(11)"`
	PushStatus   int    `xorm:"not null default 0 comment('推送状态 1：推送成功') INT(11)"`
	BackParam    string `xorm:"not null default '''' comment('商户私有域：交易返回时原样返回给商户网站，给商户备用') VARCHAR(256)"`
	ExtendInfo   string `xorm:"not null default '''' comment('扩展信息：预留字段，JSON 格式') VARCHAR(256)"`
}
