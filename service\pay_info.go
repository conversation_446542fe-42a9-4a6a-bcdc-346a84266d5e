package service

import (
	"fmt"
	"net/http"
	"pay-center/dto"
	"pay-center/proto/pay"
	"pay-center/util"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type Pay struct {
	//service.BaseServer
}

// @Description			付款查询
// <AUTHOR>
// @Date		 		2020-06-24
// @Param<ctx>      	Context
// @Return				请求返回的内容
func PayQuery(ctx echo.Context) error {
	request := new(dto.PayQuery)
	if err := ctx.Bind(request); err != nil {
		glog.Error("付款查询接口求参数错误 PayQuery", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "付款查询参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(request); err != nil {
		glog.Error("付款查询接口求参数效验错误 PayQuery", request, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "付款查询参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.PayQuery(client.Ctx, &pay.PayQueryRequest{
		MerchantId: request.MerchantId, // 商户号
		TradeNo:    request.TradeNo,    // 支付中心流水号
	}); err != nil {
		glog.Error(err)
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	} else {
		return ReturnJson(ctx, http.StatusOK, "查询成功", out)
	}
}

// 支付异步回调
func PayAsynNotice(c echo.Context) error {
	list := util.GetRequestParams(c)
	glog.Infof("统一订单支付异步回调PayAsynNotice 请求参数:%s", kit.JsonEncode(list))

	// 区分百度支付和电银支付
	if c.FormValue("rsaSign") != "" { // 百度支付
		return BaiduCallbackPay(c)
	} else if len(list) == 0 { //原生微信JsApi支付   参数是放在body的，list为空
		return new(PayWeiXin).WeiXinCallbackPay(c)
	}

	var in pay.PayAsynNoticeRequest

	// 验证,电银支付签名
	result := util.VerifySign(list, list["serverSign"], list["serverCert"])
	if result != nil {
		glog.Error("统一订单支付异步回调PayAsynNotice", "签名和证书不正确", list, result)
		return ReturnJson(c, http.StatusBadRequest, "签名和证书不正确")
	}

	in = pay.PayAsynNoticeRequest{
		TransState: list["transState"],
		OrderId:    list["orderId"],
		TradeNo:    list["tradeNo"],
		ChannelNo:  list["channelNo"],
		PayTime:    list["payTime"],
	}

	// 兼容网银支付
	if list["status"] == "SUCCESS" {
		in.TransState = "S"
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.PayInfo.PayAsynNotice(client.Ctx, &in); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, err)
	} else {
		if out.Code == http.StatusOK {
			return c.JSON(http.StatusOK, map[string]string{"result": "success"})
		} else {
			return c.JSON(http.StatusOK, out)
		}
	}
}

// 统一订单
func UnifiedOrder(c echo.Context) error {
	var (
		err error
	)
	logPrefix := "统一订单paycenter-UnifiedOrder===="
	payinfo := new(dto.PayInfo)
	if err = c.Bind(payinfo); err != nil {
		glog.Error(logPrefix, "统一订单请求出错UnifiedOrder", payinfo)
		return ReturnJson(c, http.StatusBadRequest, "参数有误")
	}
	logPrefix = fmt.Sprintf("%s，OutTradeNo:%s ", logPrefix, payinfo.OutTradeNo)
	glog.Info(logPrefix, "入参：", kit.JsonEncodeBeuty(payinfo))
	//对数据进行验证
	if err = util.ValidatorCheck(payinfo); err != nil {
		glog.Error(logPrefix, "统一订单UnifiedOrder提交的参数有问题", payinfo, err.Error())
		return ReturnJson(c, http.StatusBadRequest, err.Error())
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.PayInfo.UnifiedOrder(client.Ctx, &pay.UnifiedOrderRequest{
		TransType:    payinfo.TransType,
		OutTradeNo:   payinfo.OutTradeNo,
		PayPrice:     payinfo.PayPrice,
		TotalPrice:   payinfo.TotalPrice,
		Discount:     payinfo.Discount,
		ProductId:    payinfo.ProductId,
		ProductName:  payinfo.ProductName,
		ProductDesc:  payinfo.ProductDesc,
		ClientIP:     payinfo.ClientIP,
		Openid:       payinfo.Openid,
		SubAppId:     payinfo.SubAppId,
		MerchantId:   payinfo.MerchantId,
		NotifyUrl:    payinfo.OfflineNotifyUrl,
		ExtendInfo:   payinfo.ExtendInfo,
		OrderId:      payinfo.OrderId,
		ValidTime:    payinfo.ValidTime,
		OrderPayType: payinfo.OrderPayType,
		AppId:        payinfo.AppId,
	}); err != nil {
		glog.Error(logPrefix, err)
		return c.JSON(400, err)
	} else {
		glog.Info(logPrefix, "返回结果：", kit.JsonEncode(out))
		return c.JSON(http.StatusOK, out)
	}
}

// @Summary 标准终端绑定
// @Tags 标准终端
// @Accept json
// @Produce json
// @Param model body pay.StdBindingRequest true " "
// @Success 200 {object} pay.StdBindingResponse
// @Failure 400 {object} pay.StdBindingResponse
// @Router /pay/dy/stdbinding [POST]
func StdBinding(ctx echo.Context) error {
	model := new(pay.StdBindingRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("标准终端绑定接口求参数错误 StdBinding", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "标准终端绑定参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("标准终端绑定接口求参数错误 StdBinding", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "标准终端绑定参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.StdBinding(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 标准终端解绑
// @Tags 标准终端
// @Accept json
// @Produce json
// @Param tsn query string true "机具编号"
// @Param payType query string false "默认3：竖屏B扫C，12：B扫C（标准）"
// @Param dyMchNo query string false "电银商户号"
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/dy/stdunbinding [Get]
func StdUnBinding(ctx echo.Context) error {
	model := new(pay.StdUnBindingRequest)
	//if err := ctx.Bind(model); err != nil {
	//	glog.Error("标准终端解绑接口求参数错误StdUnBinding", model, err.Error())
	//	return ReturnJson(ctx, http.StatusOK, "标准终端解绑参数错误", err.Error())
	//}
	model.Tsn = ctx.QueryParam("tsn")
	model.PayType = cast.ToInt32(ctx.QueryParam("payType"))
	model.DyMchNo = ctx.QueryParam("dyMchNo")
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("标准终端绑定接口求参数错误StdUnBinding", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "标准终端解绑参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.StdUnBinding(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 标准终端状态查询
// @Tags 标准终端
// @Accept json
// @Produce json
// @Param tsn query string true "机具编号"
// @Success 200 {object} pay.QueryStsResponse
// @Failure 400 {object} pay.QueryStsResponse
// @Router /pay/dy/querysts [Get]
func QuerySts(ctx echo.Context) error {
	model := new(pay.QueryStsRequest)
	//if err := ctx.Bind(model); err != nil {
	//	glog.Error("标准终端状态查询接口求参数错误QuerySts", model, err.Error())
	//	return ReturnJson(ctx, http.StatusOK, "标准终端状态查询绑定参数错误", err.Error())
	//}
	model.OrgNumber = "245"
	model.Tsn = ctx.QueryParam("tsn")
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("标准终端绑定接口求参数错误QuerySts", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "标准终端状态查询绑定参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.QuerySts(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 支付电银B2C
// @Tags 支付
// @Accept json
// @Produce json
// @Param model body pay.PayForB2CRequest true " "
// @Success 200 {object} pay.PayForB2CResponse
// @Failure 400 {object} pay.PayForB2CResponse
// @Router /pay/dy/payb2c [POST]
func PayB2C(ctx echo.Context) error {
	model := new(pay.PayForB2CRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("支付电银B2C接口求参数错误PayB2C", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("支付电银B2C接口求参数错误PayB2C", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
	}
	model.ClientIp = ctx.RealIP()
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.PayForB2C(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 支付状态查询
// @Tags 支付
// @Accept json
// @Produce json
// @Param model body pay.QueryPayStatusRequest true " "
// @Success 200 {object} pay.QueryPayStatusResponse
// @Failure 400 {object} pay.QueryPayStatusResponse
// @Router /pay/dy/querypaystatus [POST]
func QueryPayStatus(ctx echo.Context) error {
	model := new(pay.QueryPayStatusRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("支付状态查询接口求参数错误QueryPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("支付状态查询接口求参数错误QueryPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "支付状态查询参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.QueryPayStatus(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 退款状态查询
// @Tags 退款
// @Accept json
// @Produce json
// @Param model body pay.QueryRefundStatusRequest true " "
// @Success 200 {object} pay.QueryRefundStatusResponse
// @Failure 400 {object} pay.QueryRefundStatusResponse
// @Router /refund/dy/queryrefundstatus [POST]
func QueryRefundStatus(ctx echo.Context) error {
	model := new(pay.QueryRefundStatusRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("退款状态查询接口求参数错误QueryRefundStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "退款状态查询绑定参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("退款状态查询接口求参数错误QueryRefundStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "退款状态查询绑定参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayRefund.QueryRefundStatus(client.Ctx, model); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 新退款
// @Tags 退款
// @Accept json
// @Produce json
// @Param model body dto.NewRefund true " "
// @Success 200 {object} pay.NewRefundResponse
// @Failure 400 {object} pay.NewRefundResponse
// @Router /refund/dy/newrefund [POST]
func NewRefund(ctx echo.Context) error {
	model := new(dto.NewRefund)
	if err := ctx.Bind(model); err != nil {
		glog.Error("新退款接口求参数错误NewRefund", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "新退款绑定参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("新退款绑定接口求参数错误NewRefund", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "新退款绑定参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	payRequest := new(pay.NewRefundRequest)
	payRequest.MerOrderNo = model.MerOrderNo
	payRequest.Trancde = model.Trancde
	payRequest.RefundAmount = model.RefundAmount
	payRequest.RefundRemark = model.RefundRemark
	payRequest.NotifyUrl = model.NotifyUrl

	headBase := new(pay.ScanHeadBase)
	headBase.TrmId = model.HeadBase.TrmId
	headBase.TrmSn = model.HeadBase.TrmSn
	headBase.MercId = model.HeadBase.MercId
	payRequest.HeadBase = headBase

	if out, err := client.PayRefund.NewRefund(client.Ctx, payRequest); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 电银支付
// @Tags 支付
// @Accept json
// @Produce json
// @Param model body dto.DyPay true " "
// @Success 200 {object} pay.DYPayResponse
// @Failure 400 {object} pay.PayForB2CResponse
// @Router /pay/dy/dypay [POST]
func DYPay(ctx echo.Context) error {
	model := new(dto.DyPay)
	if err := ctx.Bind(model); err != nil {
		glog.Error("电银支付接口求参数错误DYPay", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "电银支付参数错误", err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("电银支付接口求参数错误DYPay", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "电银支付参数错误", err.Error())
	}
	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	payRequest := new(pay.PayForB2CRequest)
	payRequest.BarCode = model.BarCode
	payRequest.OutOrderNo = model.OrderNo
	payRequest.PayType = model.PayType
	payRequest.TotalAmount = model.PayAmount
	payRequest.PayAmount = model.PayAmount
	payRequest.Discount = model.Discount
	payRequest.OrderName = model.OrderName
	payRequest.OrderDesc = model.OrderName
	payRequest.NotifyUrl = model.NotifyUrl

	headBase := new(pay.ScanHeadBase)
	headBase.TrmId = model.TrmId
	headBase.TrmSn = model.TrmSn
	headBase.MercId = model.MerchantId
	payRequest.HeadBase = headBase

	if out, err := client.PayInfo.PayForB2C(client.Ctx, payRequest); err != nil {
		glog.Error(err)
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
	return ctx.JSON(http.StatusOK, nil)
}
