package service

import (
	"net/http"
	"pay-center/util"

	"github.com/go-redis/redis"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// 重定向到网银支付收银台
func RedirectBankPay(ctx echo.Context) error {
	tradeNo := ctx.QueryParam("trade_no")
	if tradeNo == "" {
		return ctx.HTML(http.StatusBadRequest, "交易单号不能为空")
	}
	redisConn := util.GetPayRedisConn()
	defer redisConn.Close()
	val, err := redisConn.Get("pay-center:bank-pay:" + tradeNo).Result()
	if err == nil {
		if val != "" {
			return ctx.HTML(http.StatusOK, val)
		}
		glog.Errorf("RedirectBankPay 网银支付重定向数据为空 %s", tradeNo)
		return ctx.HTML(http.StatusBadRequest, "数据缺失，无法完成支付:"+tradeNo)
	}
	if err == redis.Nil {
		glog.Errorf("RedirectBankPay 无效单号，订单未创建 %s", tradeNo)
		return ctx.HTML(http.StatusBadRequest, "无效单号，订单未创建:"+tradeNo)
	}
	glog.Errorf("RedirectBankPay 系统错误，稍后再试 %s", tradeNo)
	return ctx.HTML(http.StatusBadRequest, "系统错误，稍后再试:"+tradeNo)
}
