package routers

import (
	"pay-center/middleware"
	"pay-center/service"

	"github.com/labstack/echo/v4"
)

// 路由启动
func InitRouter(e *echo.Echo) *echo.Echo {

	// 路由管理
	post := e.Group("/pay")
	{
		//统一订单
		post.POST("/unifiedorder", service.UnifiedOrder, middleware.CheckSign)
		//电银支付异步回调
		post.POST("/pay_asyn_notice", service.PayAsynNotice, middleware.SetLog)
		// 支付订单查询
		post.POST("/query", service.PayQuery, middleware.CheckSign)
		// 退款
		post.POST("/refund", service.PayRefund, middleware.CheckSign)
		// 退款查询
		post.POST("/refundquery", service.PayRefundQuery, middleware.CheckSign)
		// 电银支付
		post.POST("/dy/dypay", service.DYPay, middleware.CheckSign)
		// 退款状态
		post.POST("/refund/status", service.PayRefund, middleware.CheckSign)
		// 电银退款结果异步回调通知
		post.POST("/refundnotice", service.RefundNotification)

		// 标准终端绑定
		post.POST("/dy/stdbinding", service.StdBinding)
		// 标准终端解绑
		post.GET("/dy/stdunbinding", service.StdUnBinding)
		// 标准终端状态查询
		post.GET("/dy/querysts", service.QuerySts)
		// 支付查询
		post.POST("/dy/querypaystatus", service.QueryPayStatus)
		// 电银支付B2C
		post.POST("/dy/payb2c", service.PayB2C)

		post.GET("/test", func(c echo.Context) error {
			return c.String(200, "ok")
		})

		// 标准支付统一接口
		payApi := post.Group("/pay-api")
		{
			// 支付
			payApi.POST("/pay", service.StandardPay, middleware.CheckSignForStandardPay)
			// 支付状态
			payApi.POST("/pay/status", service.StandardPayStatus, middleware.CheckSignForStandardPay)
			// 关闭支付
			payApi.POST("/pay/close", service.StandardPayClose, middleware.CheckSignForStandardPay)
			// 退款
			payApi.POST("/pay/refund", service.StandardPayRefund, middleware.CheckSignForStandardPay)
			// 获取支付配置
			payApi.GET("/config", service.StandardPayConfig)

			//订单详情（手续费）暂时只能查询电银线上帐号的订单详情
			payApi.POST("/pay/dy-pay-info", service.DYPayInfoQuery, middleware.CheckSignForStandardPay)
			//退款订单详情（手续费）暂时只能查询电银线上帐号的退款订单详情
			payApi.POST("/pay/dy-refund-info", service.DYRefundInfo, middleware.CheckSignForStandardPay)
		}
		// 重定向到网银支付收银台
		post.GET("/bank/redirect", service.RedirectBankPay)

		// 百度支付
		bd := post.Group("/baidu")
		{
			// 回调相关，支付回调(pay_asyn_notice)和退款回调(refundnotice)走老接口，和电银回调公用
			//bd.POST("/callback/pay", service.BaiduCallbackPay, middleware.SetBaiduLog)
			//bd.POST("/callback/refund", service.BaiduCallbackRefund, middleware.SetBaiduLog)
			// 退款申请回调和退款申请，目前业务用不到
			bd.POST("/callback/refund-apply", service.BaiduCallbackRefundApply, middleware.SetBaiduLog)
			// 订单退款申请
			bd.POST("/order/refund-apply", service.BaiduOrderRefundApply, middleware.SetBaiduLog)
			// 订单关闭
			bd.POST("/order/close", service.BaiduOrderClose, middleware.SetBaiduLog)
			// 订单退款查询
			bd.GET("/order/refund-get", service.BaiduOrderRefundGet, middleware.SetBaiduLog)
		}
	}

	dy := e.Group("/refund")
	{
		// 退款查询
		dy.POST("/dy/queryrefundstatus", service.QueryRefundStatus)
		// 新退款
		dy.POST("/dy/newrefund", service.NewRefund)
	}

	account := e.Group("/pay/account", middleware.CheckChannel())
	{
		//查询是否设置过密码
		account.POST("/pwd/query", service.QueryPwd)
		//设置余额支付密码-首次
		account.POST("/pwd/set", service.SetPwd)
		//发送短信验证码
		account.POST("/pwd/sendcode", service.SendCode)
		//通过手机验证码修改密码
		account.POST("/pwd/update-mobile", service.UpdateByMobile)
		//通过旧密码修改密码
		account.POST("/pwd/update-pwd", service.UpdateByPwd)
		//获取支付方式
		account.GET("/pay/list", service.PayList)
		//检查验证码
		account.POST("/pwd/check-code", service.CheckCode)
	}

	card := e.Group("/pay/card", middleware.CheckChannel())
	{
		//获取储蓄卡余额
		card.GET("/balance", service.GetCardsBalance)
		//获取储蓄卡列表
		card.GET("/list", service.GetCards)
		//储蓄卡支付
		card.POST("/pay", service.CardPay)
		//储蓄卡退款
		card.POST("/refund", service.CardRefund)
		//储蓄卡交易查询
		card.POST("/order-record", service.CardOrderRecord)
		//储蓄卡交易流水明细
		card.POST("/record", service.CardRecord)
		//获取支付方式
		card.GET("/pay-type", service.CheckPayType)
		//发送短信验证码
		card.POST("/sendcode", service.SendCode)
	}

	return e
}
