package util

import (
	"math"
	"math/rand"
	"strconv"
	"time"
)

// @Description			字符串转INT
// <AUTHOR>
// @Date		 		2020-06-05
// @Param<str> 			字符串
// @Return				返回int值
func StringToInt(str string) int {
	if Trim(str) == "" {
		return 0
	}
	value, err := strconv.Atoi(str)
	if err != nil {
		panic(err)
	}
	return value
}

func StringToInt32(s string) int32 {
	if s == "" {
		return 0
	}
	s32, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		panic(err)
	}
	return int32(s32)
}

func StringToFloat32(s string) float32 {
	s32, _ := strconv.ParseFloat(s, 32)
	return float32(s32)
}

//float32 转 String工具类，保留6位小数
func FloatToString(input_num float32) string {
	return strconv.FormatFloat(float64(input_num), 'f', 2, 64)
}

//四舍五入返回百分比
//prec:要保留多少位小数
func DivisionRate(x, y string, prec int) string {
	var px, py float64
	var err error
	if x == "" || x == "0" || y == "" || y == "0" {
		return "0"
	}
	px, err = strconv.ParseFloat(x, 64)
	if err != nil {
		return "0"
	}
	py, err = strconv.ParseFloat(y, 64)
	if err != nil {
		return "0"
	}
	return strconv.FormatFloat(px/py*100, 'f', prec, 64) + "%"
}

//四舍五入返回商
//prec:要保留多少位小数
func Division(x, y string, prec int) string {
	var px, py float64
	var err error
	if x == "" || x == "0" || y == "" || y == "0" {
		return "0"
	}
	px, err = strconv.ParseFloat(x, 64)
	if err != nil {
		return "0"
	}
	py, err = strconv.ParseFloat(y, 64)
	if err != nil {
		return "0"
	}
	return strconv.FormatFloat(px/py, 'f', prec, 64)
}

//生成指定长度的随机数字
func RandInt(length int) int {
	return rand.New(rand.NewSource(time.Now().UnixNano())).Intn(int(math.Pow10(length)))
}
