package middleware

import (
	"testing"
)

func Test_check(t *testing.T) {
	type args struct {
		data map[string]string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		//帮我写一个测试用例
		{
			name: "test1",
			args: args{
				data: map[string]string{
					"transType":        "1",
					"outTradeNo":       "80075992068520700222657",
					"orderId":          "800759920685207002",
					"payPrice":         "1811",
					"discount":         "0",
					"totalPrice":       "1811",
					"openid":           "orvyL5RofxtkXGlXO2zPAKVvMdeI",
					"subAppId":         "wx443c57d6371fa136",
					"productId":        "214414",
					"productName":      "实物订单_800759920685207002",
					"appId":            "7",
					"orderPayType":     "03",
					"productDesc":      "real_order_800759920685207002",
					"merchantId":       "O-pb24gQXV0aG9yaXR5M",
					"offlineNotifyUrl": "https://uat1.upetmart.rvet.cn/mobile/api/payment/wxpay_jsapi/notify_url.php",
					"clientIP":         "**************",
					"validTime":        "14",
					"sign":             "5C814A0960A722E3CABCFE03DE966C08",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := check(tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("check() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
