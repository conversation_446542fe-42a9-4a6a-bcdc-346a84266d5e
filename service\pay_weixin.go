package service

import (
	"context"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/partnerpayments"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"net/http"
	"pay-center/proto/pay"
)

type PayWeiXin struct {
}

func (p *PayWeiXin) WeiXinCallbackPay(c echo.Context) error {
	var (
		wechatpayPublicKeyID = config.GetString("WxPayConfig.PubKeyId")   // 微信支付公钥ID，注意微信支付公钥ID带有PUB_KEY_ID_前缀
		pubKeyPath           = config.GetString("WxPayConfig.PubKeyPath") //APIv3 微信支付公钥文件路径
		apiV3Key             = config.GetString("WxPayConfig.ApiV3Key")   //APIv3密钥 商户解密APIv3的回调通知使用
	)

	var in pay.PayAsynNoticeRequest

	wechatpayPublicKey, err := utils.LoadPublicKeyWithPath(pubKeyPath)
	if err != nil {
		glog.Error("WeiXinCallbackPay-load wechatpay public key err：", err)
		return p.WeiXinCallbackPayReturnERROR(c)
	}

	//// 加载服务商私钥
	//privateKey, err := utils.LoadPrivateKeyWithPath(wxConfig.WxPayConfig.PrivateKeyPath)
	//if err != nil {
	//	return
	//}

	// 创建微信支付客户端
	ctx := context.Background()

	// 初始化 Client
	//opts := []core.ClientOption{
	//	option.WithWechatPayPublicKeyAuthCipher(
	//		wxConfig.WxPayConfig.SpMchid, //商户号
	//		wxConfig.WxPayConfig.SpMchSerialNo, privateKey,
	//		wechatpayPublicKeyID, wechatpayPublicKey),
	//}
	//client, err := core.NewClient(ctx, opts...)

	// 初始化 notify.Handler
	handler, err := notify.NewRSANotifyHandler(apiV3Key, verifiers.NewSHA256WithRSAPubkeyVerifier(wechatpayPublicKeyID, *wechatpayPublicKey))
	if err != nil {
		glog.Error("WeiXinCallbackPay-初始化notify.Handler失败：", err)
		return p.WeiXinCallbackPayReturnERROR(c)
	}

	transaction := new(partnerpayments.Transaction)
	request, err := handler.ParseNotifyRequest(ctx, c.Request(), transaction)
	if err != nil {
		glog.Error("WeiXinCallbackPay-ParseNotifyRequest失败：", err)
		return p.WeiXinCallbackPayReturnERROR(c)
	}

	glog.Info("WeiXinCallbackPay-请求参数：", request, transaction)

	in = pay.PayAsynNoticeRequest{
		TransState: "F",
		OrderId:    *transaction.OutTradeNo,
		TradeNo:    *transaction.TransactionId,
		ChannelNo:  *transaction.TransactionId,
		PayTime:    *transaction.SuccessTime,
	}
	if *transaction.TradeState == "SUCCESS" {
		in.TransState = "S"
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.PayInfo.PayAsynNotice(client.Ctx, &in); err != nil {
		glog.Error("WeiXinCallbackPay回调失败：", err, in)
		return p.WeiXinCallbackPayReturnERROR(c)
	} else {
		if out.Code == http.StatusOK {
			return c.JSON(http.StatusOK, nil)
		} else {
			return p.WeiXinCallbackPayReturnERROR(c)
		}
	}
}

func (p *PayWeiXin) WeiXinCallbackPayReturnERROR(c echo.Context) error {
	return c.JSON(http.StatusBadRequest, map[string]interface{}{"code": "FAIL", "message": "失败"})
}
