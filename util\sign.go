package util

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"
	"pay-center/dao"
	"pay-center/pkg/microservice/common/config"
	"pay-center/pkg/pkcs12"
	"sort"
	"strings"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 对电银提交的数据进行加密
func DySign(data map[string]string) (map[string]string, error) {
	var (
		err           error
		encryptedData []byte
		privateKeys   []interface{}
		certificates  []*x509.Certificate
	)
	//定义一个slice，用来对key进行排序
	s := make([]string, len(data))
	for k := range data {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	//参数拼成字符串
	str := ""
	for _, v := range s {
		if data[v] == "" || v == "serverSign" || v == "serverCert" || v == "merchantSign" || v == "merchantCert" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + data[v]
	}
	//获取证书的信息
	if privateKeys, certificates, err = getKey(); err != nil {
		return nil, err
	}
	hash := sha256.New()
	hash.Write([]byte(str))
	if encryptedData, err = rsa.SignPKCS1v15(rand.Reader, privateKeys[0].(*rsa.PrivateKey), crypto.SHA256, hash.Sum(nil)); err != nil {
		return nil, errors.New("加密失败")
	}
	data["merchantSign"] = strings.ToUpper(hex.EncodeToString(encryptedData))
	data["merchantCert"] = strings.ToUpper(hex.EncodeToString(certificates[0].Raw))
	//cert := base64.StdEncoding.EncodeToString(certificates[0].Raw)
	return data, nil
}

// 解密证书
func getKey() ([]interface{}, []*x509.Certificate, error) {
	var (
		keyFile      []byte
		err          error
		privateKeys  []interface{}
		certificates []*x509.Certificate
	)

	if keyFile, err = ioutil.ReadFile(config.GetString("cert.name")); err != nil {
		return nil, nil, errors.New("找不到证书文件")
	}

	if privateKeys, certificates, err = pkcs12.DecodeAll(keyFile, config.GetString("cert.password")); err != nil {
		return nil, nil, errors.New("解密证书失败")
	}

	return privateKeys, certificates, err
}

// 支付中心加密,获得加密后的sign
func MakeSign(engine *xorm.Engine, data map[string]string) (string, error) {
	var (
		err error
	)
	merchant_id := data["merchantId"]
	//如果是逍宠，取配置的
	if data["appId"] == "9" {
		merchant_id = config.GetString("merchant-id")
	}

	if merchant_id == "" {
		return "", errors.New("merchantId不能为空")
	}

	//定义一个slice，用来对key进行排序
	s := make([]string, len(data))
	for k := range data {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)

	//参数拼成字符串
	str := ""
	for _, v := range s {
		if data[v] == "" || v == "sign" {
			continue
		}

		if str != "" {
			str += "&"
		}
		str += v + "=" + data[v]
	}

	if str == "" {
		return "", errors.New("签名失败")
	}
	fmt.Println(str)
	//取出商户信息
	item := dao.Merchant{}
	if _, err = engine.Table("merchant").Where("id = ?", strings.TrimLeft(merchant_id, "O-")).Get(&item); err != nil {
		return "", err
	}
	if item.Id == "" {
		return "", errors.New("merchantId不存在")
	}

	//进行加密
	sign := strings.ToUpper(MD5(MD5(str) + item.Sercret))
	fmt.Println(sign)
	return sign, nil
}

// @Description			效验签名
// <AUTHOR>
// @Date		 		2020-06-05
// @Param<signData> 	签名数据
// @Param<serverSign> 	服务器签名
// @Param<serverCert> 	服务器证书
// @Return				返回错误信息
func VerifySign(signData map[string]string, serverSign, serverCert string) error {
	// 排序、系列化参数
	signString := SignString(signData)

	// 对base64编码的签名内容进行解码，返回签名字节
	dataByte := []byte(signString)
	signByte, err := hex.DecodeString(serverSign)
	if err != nil {
		return err
	}
	certByte, err := hex.DecodeString(serverCert)
	if err != nil {
		return err
	}

	// 选择hash算法，对需要签名的数据进行hash运算
	hash := sha256.New()
	hash.Write(dataByte)

	// 读取公钥文件，解析出公钥对象
	_, restByte := pem.Decode(certByte)
	asnData, err := x509.ParseCertificate(restByte)
	if err != nil {
		return err
	}

	// 解析公钥证书内容
	if puKey, ok := asnData.PublicKey.(*rsa.PublicKey); ok {
		// 4、RSA验证数字签名（参数是公钥对象、哈希类型、签名文件的哈希串、签名后的字节）
		return rsa.VerifyPKCS1v15(puKey, crypto.SHA256, hash.Sum(nil), signByte)
	}
	return errors.New("解析公钥证书内容")
}

// 百度支付验签：对采用sha1算法进行签名后转base64格式的数据进行验签，PS:注意，需使用【平台公钥】，而非开发者公钥
func VerifySignForBaiduPay(originalData, signData string) error {
	//publicKey := config.GetString("baidu-pay-rsaPublicKey")
	publicKey := `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCs/Aekaq9mucopc4vJ4f5F0r9LXKHoLpN3TrSzQRjsdv6hiefL7OyC+P1voSRXXR6FjGQw+sAVLw4VXxRCRuQjZziqE8IhQHKY1xFPWYq/KTSQckPZXmema56f80ebZze1n9ON81uu0QN34R4RhjB0ozS5iKrZB/4zgBfHh79MqwIDAQAB
-----END PUBLIC KEY-----`
	sign, err := base64.StdEncoding.DecodeString(signData)
	if err != nil {
		return err
	}
	// public, _ := base64.StdEncoding.DecodeString(pubKey)

	block, _ := pem.Decode([]byte(publicKey))
	if block == nil {
		return errors.New("decode pem failed")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return err
	}
	pub := pubInterface.(*rsa.PublicKey)

	hash := sha1.New()
	hash.Write([]byte(originalData))
	return rsa.VerifyPKCS1v15(pub, crypto.SHA1, hash.Sum(nil), sign)
}

// 百度支付参数排序
func EncodeForBaidu(v map[string][]string) string {
	var buf strings.Builder
	keys := make([]string, 0, len(v))
	for k := range v {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		vs := v[k]
		keyEscaped := kit.UrlEncode(k)
		for _, v := range vs {
			if buf.Len() > 0 {
				buf.WriteByte('&')
			}
			buf.WriteString(keyEscaped)
			buf.WriteByte('=')
			buf.WriteString(kit.UrlEncode(v))
		}
	}
	return buf.String()
}

// @Description			Map系列化签名数据
// <AUTHOR>
// @Date		 		2020-06-08
// @Param<signData> 	签名数据
// @Return				返回请求参数Map
func SignString(signData map[string]string) string {
	// 定义一个slice，用来对key进行排序
	sortMap := make([]string, len(signData))
	for v := range signData {
		sortMap = append(sortMap, v)
	}
	// 进行排序
	sort.Strings(sortMap)
	// 参数拼成字符串
	signString := ""
	for _, v := range sortMap {
		if signData[v] == "" || v == "serverSign" || v == "serverCert" || signData[v] == "null" || signData[v] == "NULL" {
			continue
		}
		if signString != "" {
			signString += "&"
		}
		signString += v + "=" + signData[v]
	}
	return signString
}

// 标注支付接口签名生成,payCenter服务中也有
func MakeSignForStandardPay(data map[string]string) (string, error) {
	// A、参数校验，app_id,timestamp,secret
	appId := data["app_id"]
	timestamp := data["timestamp"]
	if cast.ToInt(appId) < 1 || cast.ToInt(appId) > 7 { // 1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS 6：上海兽丘  7:深圳SAAS
		return "", errors.New("app_id错误")
	}
	if len(timestamp) != 13 {
		return "", errors.New("时间戳错误")
	}
	secret := ""
	switch appId {
	case "2":
		secret = config.GetString("standardPaySecret_zl")
	case "3":
		secret = config.GetString("standardPaySecret_r")
	case "4":
		secret = config.GetString("standardPaySecret_hos")
	case "5", "7":
		secret = config.GetString("standardPaySecret_saas")
	case "6":
		secret = config.GetString("standardPaySecret_sq")
	default:
		secret = config.GetString("standardPaySecret")
	}
	if secret == "" {
		return "", errors.New("查询secret失败")
	}

	//B、字典排序处理，定义一个slice，用来对key进行排序
	s := make([]string, len(data))
	for k := range data {
		s = append(s, k)
	}
	sort.Strings(s)

	//C、参数拼成字符串
	str := ""
	for _, v := range s {
		if data[v] == "" || v == "sign" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + data[v]
	}
	if str == "" {
		return "", errors.New("签名失败")
	}

	// D、进行加密
	str += "&secret=" + secret
	sign := strings.ToUpper(MD5(str))
	return sign, nil
}
