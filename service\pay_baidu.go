package service

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"pay-center/proto/pay"
	"pay-center/util"
)

/**
百度小程序支付相关接口
文档地址：https://smartprogram.baidu.com/docs/develop/function/tune_up_2.0/
*/

type PayBaidu struct {
}

func BaiduCallbackPay(c echo.Context) error {
	var in pay.PayAsynNoticeRequest
	list := util.GetRequestParams(c)

	glog.Info("BaiduCallbackPay回调请求日志：", kit.JsonEncode(list))

	// 走百度支付签名逻辑
	signData := make(map[string][]string)
	for k, v := range list {
		if k == "rsaSign" {
			continue
		}
		signData[k] = []string{v}
	}
	err := util.VerifySignForBaiduPay(util.EncodeForBaidu(signData), list["rsaSign"])
	if err != nil {
		glog.Error("统一订单支付异步回调PayAsynNotice百度支付BaiduCallbackPay签名错误", err, kit.JsonEncode(list))
		return ReturnJson(c, http.StatusBadRequest, "百度支付签名错误")
	}

	in = pay.PayAsynNoticeRequest{
		TransState: "F",
		OrderId:    list["tpOrderId"],
		TradeNo:    list["orderId"],
		ChannelNo:  list["partnerId"],
		PayTime:    list["payTime"],
	}
	if list["status"] == "2" {
		in.TransState = "S"
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.PayInfo.PayAsynNotice(client.Ctx, &in); err != nil {
		glog.Error("BaiduCallbackPay回调失败：", err)
		return c.JSON(http.StatusBadRequest, err)
	} else {
		if out.Code == http.StatusOK {
			return c.JSON(http.StatusOK, map[string]interface{}{
				"errno": 0,
				"msg":   "success",
				"data": map[string]int{
					"isConsumed": 2,
				},
			})
		} else {
			return c.JSON(http.StatusOK, out)
		}
	}
}

func BaiduCallbackRefundApply(c echo.Context) error {
	return c.JSON(http.StatusOK, echo.Map{
		"errno": 0,
		"msg":   "success",
		"data":  "",
	})
}

func BaiduOrderClose(c echo.Context) error {
	return c.JSON(http.StatusOK, echo.Map{
		"errno": 0,
		"msg":   "success",
		"data":  "",
	})
}

func BaiduOrderRefundApply(c echo.Context) error {
	return c.JSON(http.StatusOK, echo.Map{
		"errno": 0,
		"msg":   "success",
		"data":  "",
	})
}

// 百度支付退款查询
func BaiduOrderRefundGet(c echo.Context) error {
	tradeNo := c.QueryParam("trade_no")
	if tradeNo == "" {
		return c.JSON(http.StatusOK, echo.Map{
			"code":    400,
			"message": "参数缺失",
			"data":    "",
		})
	}
	client := pay.GetPayCenterClient()
	res, err := client.RPC.BaiduOrderRefundGet(client.Ctx, &pay.BaiduOrderRefundGetRequest{
		TradeNo: tradeNo,
	})

	if err != nil {
		return c.JSON(http.StatusOK, echo.Map{
			"code":    400,
			"message": err.Error(),
			"data":    "",
		})
	}
	return c.JSON(http.StatusOK, echo.Map{
		"code":    200,
		"message": "success",
		"data":    res,
	})
}
