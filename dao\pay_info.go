package dao

type PayInfo struct {
	Id           string `xorm:"not null pk comment('订单id') CHAR(32)"`
	TradeNo      string `xorm:"not null comment('支付中心流水号') VARCHAR(32)"`
	DyTradeNo    string `xorm:"not null default '''' comment('支付成功，电银生成的流水号') VARCHAR(32)"`
	MerchantId   string `xorm:"not null comment('支付中心分配的商户号') VARCHAR(32)"`
	DyMerchantId string `xorm:"not null default '''' comment('电银商户号') VARCHAR(32)"`
	OutTradeNo   string `xorm:"not null comment('商户流水号') VARCHAR(32)"`
	OrderTime    string `xorm:"not null comment('订单日期  格式：YYYYMMDD') VARCHAR(14)"`
	TotalPrice   int    `xorm:"not null default 0 comment('总价格      单位分') INT(11)"`
	PayPrice     int    `xorm:"not null default 0 comment('实付价格   单位分') INT(11)"`
	Status       int    `xorm:"not null default 0 comment('订单状态   0：未支付    1：已支付   2：部分退款  3：全部退款') TINYINT(4)"`
	Discount     int    `xorm:"not null default 0 comment('优惠   单位分') INT(11)"`
	Refund       int    `xorm:"not null default 0 comment('退款  单位分') INT(11)"`
	ProductId    string `xorm:"not null default '''' comment('商品编号') VARCHAR(10)"`
	ProductName  string `xorm:"not null default '''' comment('商品名称') VARCHAR(32)"`
	ProductDesc  string `xorm:"not null default '''' comment('商品描述') VARCHAR(64)"`
	NotifyUrl    string `xorm:"not null default '''' comment('后台回调地址') VARCHAR(200)"`
	ClientIp     string `xorm:"not null default '''' comment('客户端 IP') VARCHAR(30)"`
	PayType      int    `xorm:"not null default 0 comment('支付方式    1：微信 JSAPI') TINYINT(4)"`
	AddTime      int    `xorm:"not null default 0 comment('添加时间') INT(11)"`
	PayTime      int    `xorm:"not null default 0 comment('付款时间') INT(11)"`
	PushNum      int    `xorm:"not null default 0 comment('推送次数') INT(11)"`
	PushStatus   int    `xorm:"not null default 0 comment('推送状态 1：推送成功') INT(11)"`
	ChannelNo    string `xorm:"not null comment('扣款通道返回的流水号') VARCHAR(32)"`
	ExtendInfo   string `xorm:"not null comment('扩展信息 预留字段，JSON 格式') VARCHAR(255)"`
	OrderId      string `xorm:"not null comment('商户订单号') VARCHAR(32)"`
	OrgId        string `xorm:"comment('机构号') VARCHAR(55)"`
	TrmSn        string `xorm:"comment('机具编号') VARCHAR(55)"`
	TrmId        string `xorm:"comment('终端号') VARCHAR(55)"`
	OrderPayType string `xorm:"not null default '''' comment('交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)') VARCHAR(10)"`
	AppId        int    `xorm:"not null default 1 comment('应用id，1：阿闻，2：子龙，3：R1，4：互联网，默认阿闻') TINYINT(4)"`
	FeeAmt       int    `xorm:"not null default 0 comment('手续费,单位分') INT(11)"`
}
