package util

import (
	"bytes"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"io/ioutil"
	"strings"
	"time"
)

// @Description			获取时间节字符串
// <AUTHOR>
// @Date		 		2020-06-04
// @Return				返回获取时间节字符串
func GetTimeSectionString() string {
	strTime := time.Now().Format("20060102150405.000")
	return strings.Replace(strTime, ".", "", -1)
}

// @Description			去掉字符串的空格和换行符
// <AUTHOR>
// @Date		 		2020-06-03
// @Param<str>      	字符串
// @Return				返回去掉空格和换行符的字符串
func Trim(str string) string {
	// 去除空格
	str = strings.Replace(str, " ", "", -1)
	// 去除换行符
	str = strings.Replace(str, "\n", "", -1)
	return str
}

// @Description			获取求参数
// <AUTHOR>
// @Date		 		2020-06-08
// @Param<ctx> 			Context
// @Return				返回请求参数Map
func GetRequestParams(ctx echo.Context) map[string]string {
	params, _ := ctx.FormParams()
	requestMap := make(map[string]string)
	for k, v := range params {
		requestMap[k] = v[0]
	}
	return requestMap
}

// 获取body中json参数
func GetBodyParams(ctx echo.Context) map[string]string {
	var params map[string]interface{}

	// 针对body数据多次读取，后面读取不到问题处理
	bodyBytes, _ := ioutil.ReadAll(ctx.Request().Body)
	err := json.Unmarshal(bodyBytes, &params)
	if err != nil {
		glog.Error("GetBodyParams签名获取body参数错误", err)
	}
	ctx.Request().Body.Close() //  must close
	ctx.Request().Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))

	requestMap := make(map[string]string)
	for k, v := range params {
		requestMap[k] = cast.ToString(v)
	}
	return requestMap
}
