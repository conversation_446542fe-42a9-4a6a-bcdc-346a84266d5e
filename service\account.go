package service

import (
	"fmt"
	"net/http"
	"pay-center/dto"
	"pay-center/proto/dac"
	"pay-center/proto/pay"
	"pay-center/proto/pm"
	"pay-center/util"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	grpc "google.golang.org/grpc"
)

type Account struct {
	//service.BaseServer
}

// @Summary 查询是否设置过密码
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.QueryPwdRequest true " "
// @Success 200 {object} pay.QueryPwdResponse
// @Failure 400 {object} pay.QueryPwdResponse
// @Router /pay/account/pwd/query [POST]
func QueryPwd(ctx echo.Context) error {
	model := new(pay.QueryPwdRequest)
	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, http.StatusBadRequest, "用户不存在")
	}

	model.Mobile = memberInfo.Mobile
	if util.VerifyMobileFormat(model.Mobile) == false {
		return ReturnJson(ctx, http.StatusBadRequest, "手机号格式错误")
	}

	client := GetpayInfoClient()
	defer func(Conn *grpc.ClientConn) {
		err := Conn.Close()
		if err != nil {

		}
	}(client.Conn)
	defer client.Cf()

	if out, err := client.PayInfo.QueryPwd(client.Ctx, model); err != nil {
		return ctx.JSON(http.StatusOK, err.Error())
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 设置余额支付密码-首次
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.SetPwdRequest true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/account/pwd/set [POST]
func SetPwd(ctx echo.Context) error {
	model := new(pay.SetPwdRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("设置支付密码参数错误", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "参数错误", err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, http.StatusBadRequest, "用户不存在")
	}

	model.Mobile = memberInfo.Mobile
	model.ScrmId = memberInfo.ScrmUserId
	if util.VerifyMobileFormat(model.Mobile) == false {
		return ReturnJson(ctx, http.StatusBadRequest, "手机号格式错误")
	}

	client := GetpayInfoClient()
	defer func(Conn *grpc.ClientConn) {
		err := Conn.Close()
		if err != nil {

		}
	}(client.Conn)
	defer client.Cf()

	if out, err := client.PayInfo.SetPwd(client.Ctx, model); err != nil {
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 发送短信验证码
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.QueryPwdRequest true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/account/pwd/sendcode [POST]
func SendCode(c echo.Context) error {
	model := new(pay.QueryPwdRequest)
	if err := c.Bind(model); err != nil {
		glog.Error("发送短信验证码参数错误", model, err.Error())
		return ReturnJson(c, http.StatusOK, "参数错误", err.Error())
	}

	memberInfo, ok := c.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(c, http.StatusBadRequest, "用户不存在")
	}
	model.Mobile = memberInfo.Mobile

	out := new(pm.BaseResponse)
	out.Code = 200
	redis := util.GetPayRedisConn()
	defer redis.Close()
	//修改密码发送验证码次数
	checkCountKey := util.RpMillionsVerifyCountKey + model.Mobile
	//支付验证码发送次数
	if model.Type == 1 {
		//redis = common.GetPayRedisConn()
		checkCountKey = util.RpMillionsPayCountKey + model.Mobile
	}

	//修改密码 获取次数限制当天内10次
	todayRemainSecond := util.TodayRemainSecond()
	intSecond := time.Duration(todayRemainSecond)
	//抢占第一次设置 验证码限制次数
	setOk := redis.SetNX(checkCountKey, 1, time.Second*intSecond)
	var count int64 = 1

	if !setOk.Val() {
		//不是第一次设置，则累加计算
		count = cast.ToInt64(redis.Get(checkCountKey).Val())
		if model.Type == 0 && count >= 10 {
			return c.JSON(400, pm.BaseResponse{
				Code:    400,
				Message: "修改密码短信验证码次数已超过10次限制",
			})
		}

		if model.Type == 1 && count >= 20 {
			return c.JSON(400, pm.BaseResponse{
				Code:    400,
				Message: "支付短信验证码次数已超过20次限制",
			})
		}
	}

	// 发送短信
	dacClient := dac.GetDataCenterClient()
	code := util.GenValidateCode(6)
	req := &dac.SendSmsRequest{
		PhoneNumbers:  model.Mobile,
		TemplateCode:  util.TemplateCodeLotteryNotice,
		SignName:      util.SignName,
		TemplateParam: fmt.Sprintf(`{"code":"%s"}`, code),
	}
	re, err := dacClient.RPC.SendSms(dacClient.Ctx, req)
	if err != nil {
		glog.Error("发送短信失败err", err)
		out.Code = 400
		return c.JSON(int(out.Code), out)
	}
	if re.Code != 200 {
		out.Code = 400
		if re.Data.Code == "isv.BUSINESS_LIMIT_CONTROL" {
			out.Message = "您发送的太频繁，请稍后再发！"
		} else {
			out.Message = "发送失败！"
			glog.Error("发送短信失败，", re.Data.Code, "，err：", string(re.Message), "，参数：", req)
		}
		return c.JSON(int(out.Code), out)
	}
	glog.Info(fmt.Sprintf("发送短信验证码成功:%s %v", model.Mobile, re.Message))

	if model.Type == 1 {
		codeKey := util.RpMillionsVerifyPayCode
		redis.HSet(codeKey, model.Mobile+":"+model.OrderSn, code)
	} else {
		codeKey := util.RpMillionsVerifyCodeKey + model.Mobile
		redis.Set(codeKey, code, time.Duration(20)*time.Minute)
	}

	redis.Incr(checkCountKey) //次数加1
	return c.JSON(http.StatusOK, out)
}

// @Summary 通过手机号修改密码
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.UpdateByMobileRequest true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/account/pwd/update-mobile [POST]
func UpdateByMobile(ctx echo.Context) error {
	model := new(pay.UpdateByMobileRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("修改密码参数错误", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "参数错误", err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, http.StatusBadRequest, "用户不存在")
	}
	model.Mobile = memberInfo.Mobile
	model.ScrmId = memberInfo.ScrmUserId

	if util.VerifyMobileFormat(model.Mobile) == false {
		return ReturnJson(ctx, http.StatusBadRequest, "手机号格式错误")
	}

	redis := util.GetPayRedisConn()
	defer redis.Close()
	codeKey := util.RpMillionsVerifyCodeKey + model.Mobile //

	if !kit.EnvIsTest() {
		if redis.Get(codeKey).Val() != model.Code {
			return ReturnJson(ctx, http.StatusBadRequest, "验证码错误")
		}
	}

	client := GetpayInfoClient()
	defer func(Conn *grpc.ClientConn) {
		err := Conn.Close()
		if err != nil {

		}
	}(client.Conn)
	defer client.Cf()
	if out, err := client.PayInfo.UpdateByMobile(client.Ctx, model); err != nil {
		return ctx.JSON(http.StatusOK, err)
	} else {
		//重置密码错误次数
		key := util.PayCenterPasswordCount + memberInfo.ScrmUserId
		redis.Del(key)
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 通过原密码修改密码
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.UpdateByPwdRequest true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/account/pwd/update-pwd [POST]
func UpdateByPwd(ctx echo.Context) error {
	model := new(pay.UpdateByPwdRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("密码修改参数错误", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "参数错误", err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil || memberInfo.Mobile == "" {
		return ReturnJson(ctx, http.StatusBadRequest, "用户不存在")
	}
	model.Mobile = memberInfo.Mobile
	model.ScrmId = memberInfo.ScrmUserId

	if util.VerifyMobileFormat(model.Mobile) == false {
		return ReturnJson(ctx, http.StatusBadRequest, "手机号格式错误")
	}

	redis := util.GetPayRedisConn()
	defer redis.Close()
	client := GetpayInfoClient()
	defer func(Conn *grpc.ClientConn) {
		err := Conn.Close()
		if err != nil {

		}
	}(client.Conn)
	defer client.Cf()
	if out, err := client.PayInfo.UpdateByPwd(client.Ctx, model); err != nil {
		return ctx.JSON(http.StatusOK, err)
	} else {
		//重置密码错误次数
		key := util.PayCenterPasswordCount + memberInfo.ScrmUserId
		redis.Del(key)
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 获取支付方式
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.QueryPwdRequest true " "
// @Success 200 {object} pay.PayListResponse
// @Failure 400 {object} pay.PayListResponse
// @Router /pay/account/pay/list [GET]
func PayList(ctx echo.Context) error {
	model := new(pay.QueryPwdRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("密码修改参数错误", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "参数错误", err.Error())
	}

	client := GetpayInfoClient()
	defer func(Conn *grpc.ClientConn) {
		err := Conn.Close()
		if err != nil {

		}
	}(client.Conn)
	defer client.Cf()
	if out, err := client.PayInfo.PayList(client.Ctx, model); err != nil {
		return ctx.JSON(http.StatusOK, err)
	} else {
		return ctx.JSON(http.StatusOK, out)
	}
}

// @Summary 检查短信验证码是否正确
// @Tags 支付设置
// @Accept json
// @Produce json
// @Param model body pay.CheckCodeRequest true " "
// @Success 200 {object} pay.CheckCodeResponse
// @Failure 400 {object} pay.CheckCodeResponse
// @Router /pay/account/pwd/check-code [POST]
func CheckCode(ctx echo.Context) error {
	out := pay.CheckCodeResponse{
		Code: 200,
	}
	model := new(pay.CheckCodeRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("检查短信验证码参数错误", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, "参数错误", err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, http.StatusBadRequest, "用户不存在")
	}
	model.Mobile = memberInfo.Mobile

	if util.VerifyMobileFormat(model.Mobile) == false {
		return ReturnJson(ctx, http.StatusBadRequest, "手机号格式错误")
	}

	if model.Code == "688" && kit.EnvIsTest() {
		out.IsTrue = 1
		return ctx.JSON(http.StatusOK, out)
	}

	redis := util.GetPayRedisConn()
	defer redis.Close()
	codeKey := util.RpMillionsVerifyCodeKey + model.Mobile //
	if redis.Get(codeKey).Val() == model.Code {
		out.IsTrue = 1
	}

	return ctx.JSON(http.StatusOK, out)
}
