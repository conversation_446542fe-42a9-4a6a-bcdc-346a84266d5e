package util

import (
	"fmt"
	"strconv"
	"time"
)

func NowDateTime() string {
	now := time.Now()
	return fmt.Sprintf("%d-%d-%d %d:%d:%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second())
}

func NowDate() string {
	now := time.Now()
	return fmt.Sprintf("%d-%d-%d", now.Year(), now.Month(), now.Day())
}

//字符串转时间戳
//务必在main.go设置上海时区
func DateToTime(t string) int64 {
	tm2, err := time.ParseInLocation("2006-01-02", t, time.Local)
	if err != nil {
		return 31507200
	}
	return tm2.Unix()
}

//字符串转时间戳
//务必在main.go设置上海时区
func DatetimeToTime(t string) int64 {
	tm2, err := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	if err != nil {
		return 31507200
	}
	return tm2.Unix()
}

//时间戳转日期时间字符串
func TimeToDatetime(t string) string {
	it, _ := strconv.ParseInt(t, 10, 64)
	tm := time.Unix(it, 0)
	return tm.Format("2006-01-02 15:04:05")
}

//UTC时间转日期
func UtcToDate(t time.Time) string {
	return fmt.Sprintf("%d-%d-%d", t.Year(), t.Month(), t.Day())
}
