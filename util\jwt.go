package util

import (
	"errors"
	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo/v4"
	"log"
	jwt2 "pay-center/pkg/microservice/common/jwt"
	"strings"
)

// 参数tokenStr指的是 从客户端传来的待验证Token
// 验证Token过程中，如果Token生成过程中，指定了iat与exp参数值，将会自动根据时间戳进行时间验证
// 返回payload例子，map[exp:1.562839369e+09 iat:1.562234569e+09 iss:rp-pet.com mobile:18576 nameid:2 role:member]
func parseAndGetPayload(tokenStr string) (jwt.MapClaims, error) {
	// 基于公钥验证Token合法性
	token, err := jwt.Parse(tokenStr,
		func(token *jwt.Token) (interface{}, error) {
			// 基于JWT的第一部分中的alg字段值进行一次验证
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, errors.New("验证登录信息的加密类型错误")
			}
			//fmt.Println(jwt2.PublicKey)
			return jwt2.PublicKey, nil
		},
	)

	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if ok && token.Valid {
		/*
			//判断是否过期
			nowTime := time.Now().Unix()
			jwtTime := int64(claims["exp"].(float64))
			if nowTime-jwtTime > 0 {
				return nil, errors.New("登录信息已过期")
			}
		*/
		return claims, nil
	}
	return claims, errors.New("登录信息无效或者无对应值")
}

//系统内部使用解析token
func GetPayloadDirectly(ctx echo.Context) (jwt.MapClaims, error) {
	token := ""
	jwtToken := ctx.Request().Header.Get("Authorization")
	if jwtToken != "" {
		auths := strings.Split(jwtToken, " ")
		if len(auths) < 2 {
			return nil, errors.New("非法令牌")
		}
		protocol := auths[0]
		token = auths[1]
		if strings.ToLower(protocol) != "bearer" {
			return nil, errors.New("非法令牌")
		}

	} else {
		return nil, errors.New("无登录信息")
	}

	claims, err := parseAndGetPayload(token)
	if err != nil {
		return nil, err
	}
	log.Printf("get claims : %v", claims)
	return claims, nil
}
