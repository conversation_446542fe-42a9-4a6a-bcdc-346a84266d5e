module pay-center

go 1.13

require (
	github.com/BurntSushi/toml v0.3.1
	github.com/alecthomas/template v0.0.0-20190718012654-fb15b899a751
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-playground/locales v0.13.0
	github.com/go-playground/universal-translator v0.17.0
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/go-xorm/xorm v0.7.9
	github.com/golang/protobuf v1.4.3
	github.com/kr/pty v1.1.5 // indirect
	github.com/labstack/echo/v4 v4.1.17
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.0.0-20210318014958-1994bf11e98c
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.25
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/shopspring/decimal v1.2.0
	github.com/spf13/cast v1.3.1
	github.com/swaggo/echo-swagger v1.3.0
	github.com/swaggo/gin-swagger v1.2.0 // indirect
	github.com/swaggo/swag v1.7.9
	github.com/tricobbler/rp-kit v0.0.0-20210413075252-45df7834f17a
	github.com/wechatpay-apiv3/wechatpay-go v0.2.20
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013
	google.golang.org/grpc v1.33.2
	google.golang.org/protobuf v1.25.0
	gopkg.in/go-playground/validator.v9 v9.31.0
)
