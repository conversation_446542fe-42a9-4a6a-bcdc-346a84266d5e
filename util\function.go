package util

import (
	"crypto/md5"
	"crypto/rsa"
	"fmt"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo/v4"
)

const (
	TemplateCodeLotteryNotice = "SMS_241066101" //  账号验证短信模板【新】
	SignName                  = "阿闻宠物"
	RpMillionsVerifyCodeKey   = "RpMillions:SmsVerifyCode:"
	RpMillionsVerifyCountKey  = "RpMillions:SmsVerifyCodeCount:"
	RpMillionsPayCountKey     = "RpMillions:PayCodeCount:"
	PayCenterPasswordCount    = "paycenter:password:count:"
	RpMillionsVerifyPayCode   = "RpMillions:SmsVerifyCode:PayCode"
)

func MD5(s string) string {
	data := []byte(s)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has) //将[]byte转成16进制

	return md5str
}

//获取域名和端口
func GetDomain(c echo.Context) string {
	return "http://" + c.Request().Host
}

func VerifyMobileFormat(mobileNum string) bool {
	regular := `^(1[3-9])\d{9}$`
	reg := regexp.MustCompile(regular)
	return reg.MatchString(mobileNum)
}

func ValidateToken(token string, publicKey *rsa.PublicKey) (*jwt.Token, error) {
	jwtToken, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodRSA); !ok {
			//glog.Printf("Unexpected signing method: %v", t.Header["alg"])
			return nil, fmt.Errorf("invalid token")
		}
		return publicKey, nil
	})
	if err == nil && jwtToken.Valid {
		return jwtToken, nil
	}
	return nil, err
}

//获取当天结束剩余秒数
func TodayRemainSecond() float64 {
	todayLast := time.Now().Format("2006-01-02") + " 23:59:59"

	todayLastTime, _ := time.ParseInLocation("2006-01-02 15:04:05", todayLast, time.Local)

	remainSecond := time.Duration(todayLastTime.Unix()-time.Now().Local().Unix()) * time.Second

	return remainSecond.Seconds()
}

//随机验证码
func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	rand.Seed(time.Now().UnixNano())

	var sb strings.Builder
	for i := 0; i < width; i++ {
		fmt.Fprintf(&sb, "%d", numeric[rand.Intn(r)])
	}
	return sb.String()
}
