package service

import (
	"context"
	"encoding/json"
	"net/http"
	"pay-center/dao"
	"pay-center/dto"
	"pay-center/pkg/microservice/common/config"
	"pay-center/proto/pay"
	"pay-center/util"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/tricobbler/rp-kit/cast"
)

type payInfoContextKey struct {
}

// StandardPayClose
// @Summary 支付关闭
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body dto.PayClose true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /pay/pay-api/pay/close [POST]
func StandardPayClose(ctx echo.Context) error {
	var (
		out *pay.BaseResponse
		err error
	)
	model := new(dto.PayClose)
	if err := ctx.Bind(model); err != nil {
		glog.Error("支付关闭接口求参数错误PayClose", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}
	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("支付状态查询接口求参数错误QueryPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err = client.PayInfo.StandardPayClose(client.Ctx, &pay.StandardPayCloseRequest{
		AppId:   model.AppId,
		TradeNo: model.TradeNo,
	}); err != nil {
		glog.Error("标准支付状态查询返回错误StandardPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	return ctx.JSON(http.StatusOK, out)
}

// StandardPayStatus
// @Summary 支付状态
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body dto.StandardPayStatus true " "
// @Success 200 {object} dto.PayStatusResponse
// @Failure 400 {object} dto.PayStatusResponse
// @Router /pay/pay-api/pay/status [POST]
func StandardPayStatus(ctx echo.Context) error {
	var (
		out dto.PayStatusResponse
		err error
	)
	model := new(dto.StandardPayStatus)
	if err := ctx.Bind(model); err != nil {
		glog.Error("支付状态参数错误StandardPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}

	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("支付状态参数错误StandardPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	modelStr, _ := json.Marshal(model)
	if out.Data, err = client.PayInfo.StandardPayStatus(client.Ctx, &pay.JsonStrRequest{
		JsonByte: modelStr,
	}); err != nil {
		glog.Error("标准支付状态查询返回错误StandardPayStatus", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	out.Code = 200
	out.Message = "查询成功"
	return ctx.JSON(http.StatusOK, out)
}

// @Summary 标准支付回调说明
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param request body dto.AsyncCallback true " "
// @Router /callback-pay [POST]
func StandardPayCallback(ctx echo.Context) (err error) {
	return
}

// @Summary 标准支付退款回调说明
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param request body dto.AsyncRefundCallback true " "
// @Router /callback-refund [POST]
func StandardPayRefundCallback(ctx echo.Context) (err error) {
	return
}

// StandardPay
// @Summary 标准支付
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body dto.Pay true " "
// @Success 200 {object} dto.PayResponse
// @Failure 400 {object} dto.PayResponse
// @Router /pay/pay-api/pay [POST]
func StandardPay(ctx echo.Context) error {
	var out dto.PayResponse
	var err error
	model := new(dto.Pay)
	if err := ctx.Bind(model); err != nil {
		glog.Error("标准支付参数错误StandardPay", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "标准支付参数解析失败，"+err.Error())
	}

	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("标准支付参数错误StandardPay", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, "标准支付参数校验失败，"+err.Error())
	}
	if model.AppId != 1 && model.TransType == 12 && model.MerchantId == "" {
		return ReturnJson(ctx, http.StatusBadRequest, "trans_type=12时商户号必传")
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	modelStr, _ := json.Marshal(model)
	glog.Infof("pay_echo_StandardPay====入参：%#v", model)
	if out.Data, err = client.PayInfo.StandardPay(client.Ctx, &pay.JsonStrRequest{
		JsonByte: modelStr,
	}); err != nil {
		glog.Error("标准支付返回错误StandardPay", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	out.Code = 200
	out.Message = "请求成功"
	return ctx.JSON(http.StatusOK, out)
}

// StandardPayRefund
// @Summary 标准支付退款
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body dto.PayRefund true " "
// @Success 200 {object} dto.PayRefundResponse
// @Failure 400 {object} dto.PayRefundResponse
// @Router /pay/pay-api/pay/refund [POST]
func StandardPayRefund(ctx echo.Context) error {
	var (
		out dto.PayRefundResponse
		err error
	)
	model := new(dto.PayRefund)
	if err := ctx.Bind(model); err != nil {
		glog.Error("标准支付退款参数错误StandardPayRefund", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}

	// 对数据进行效验
	if err := util.ValidatorCheck(model); err != nil {
		glog.Error("标准支付退款参数错误StandardPayRefund", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	modelStr, _ := json.Marshal(model)
	if out.Data, err = client.PayInfo.StandardPayRefund(client.Ctx, &pay.JsonStrRequest{
		JsonByte: modelStr,
	}); err != nil {
		glog.Error("标准支付退款返回错误StandardPayRefund", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	out.Code = 200
	out.Message = "请求成功"
	return ctx.JSON(http.StatusOK, out)
}

// @Summary 标准支付开关配置（目前app端用）
// @Tags 支付中心
// @Accept json
// @Produce json
// @Success 200 {object} dto.StandardPayConfig
// @Router /pay/pay-api/config [GET]
func StandardPayConfig(ctx echo.Context) error {
	var (
		out dto.StandardPayConfig
	)

	// 获取开关配置
	appVal := config.GetString("pay_switch_app")
	glog.Info("StandardPayConfig配置读取", appVal)
	if appVal != "" { // "11"，微信，支付宝的值拼接
		defer func() {
			if err := recover(); err != nil {
				glog.Error("StandardPayConfig读取配置异常", appVal, err)
			}
		}()
		out.Data.App.OpenWx = cast.ToInt32(string(appVal[0]))
		out.Data.App.OpenAlipay = cast.ToInt32(string(appVal[1]))
	} else { // 默认值
		out.Data.App.OpenAlipay = 0
		out.Data.App.OpenWx = 0
	}

	out.Code = 200
	out.Message = "请求成功"
	return ctx.JSON(http.StatusOK, out)
}

// DYPayInfoQuery
// @Summary 标准支付订单详情
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body pay.PayInfoQueryRequest true " "
// @Success 200 {object} pay.PayInfoQueryResponse
// @Failure 400 {object} pay.PayInfoQueryResponse
// @Router /pay/dy-pay-info [POST]
func DYPayInfoQuery(ctx echo.Context) error {
	var (
		out *pay.PayInfoQueryResponse
		err error
	)
	model := new(pay.PayInfoQueryRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("标准支付订单详情参数错误PayInfoQuery", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}

	if model.MerchantId == "" {
		glog.Error("标准支付订单详情参数错误PayInfoQuery", model, "参数merchant_id错误")
		return ReturnJson(ctx, http.StatusOK, "参数merchant_id错误")
	}

	if model.OrderId == "" {
		glog.Error("标准支付订单详情参数错误PayInfoQuery", model, "参数order_id错误")
		return ReturnJson(ctx, http.StatusOK, "参数order_id错误")
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	// 注意context参数里面要传递dao.PayInfo对象，否则会panic
	clientCtx := context.WithValue(context.Background(), payInfoContextKey{}, dao.PayInfo{})

	if out, err = client.PayInfo.PayInfoQuery(clientCtx, model); err != nil {
		glog.Error("支付查询接口PayInfoQuery错误", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	return ctx.JSON(http.StatusOK, out)
}

// DYRefundInfo
// @Summary 标准退款订单详情
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param model body pay.DYRefundInfoRequest true " "
// @Success 200 {object} pay.DYRefundInfoResponse
// @Failure 400 {object} pay.DYRefundInfoResponse
// @Router /pay/dy-refund-info [POST]
func DYRefundInfo(ctx echo.Context) error {
	var (
		out *pay.DYRefundInfoResponse
		err error
	)
	model := new(pay.DYRefundInfoRequest)
	if err := ctx.Bind(model); err != nil {
		glog.Error("标准退款订单详情参数错误DYRefundInfo", model, err.Error())
		return ReturnJson(ctx, http.StatusOK, err.Error())
	}

	//if model.MerchantId == "" {
	//	glog.Error("标准退款订单详情参数错误DYRefundInfo", model, "参数MerchantId错误")
	//	return ReturnJson(ctx, http.StatusOK, "参数merchant_id错误")
	//}

	if model.TradeNo == "" {
		glog.Error("标准退款订单详情参数错误DYRefundInfo", model, "参数trade_no错误")
		return ReturnJson(ctx, http.StatusOK, "参数trade_no错误")
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err = client.PayRefund.DYRefundInfo(context.Background(), model); err != nil {
		glog.Error("支付退款接口DYRefundInfo错误", model, err.Error())
		return ReturnJson(ctx, http.StatusBadRequest, err.Error())
	}

	return ctx.JSON(http.StatusOK, out)
}
