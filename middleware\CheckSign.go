package middleware

import (
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"net/http"
	"pay-center/service"
	"pay-center/util"
)

// 鉴权
func CheckSign(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			err  error
			list = make(map[string]string)
		)
		list = util.GetRequestParams(c)

		glog.Info("请求参数:", list)

		//对参数进行检验
		if err = check(list); err != nil {
			glog.Error(err.Error(), "请求参数:", list)
			return service.ReturnJson(c, http.StatusBadRequest, err.Error())
			//return next(c)
		}

		return next(c)
	}
}

// 对参数进行检验
func check(data map[string]string) error {
	var (
		err  error
		sign string
	)
	engine := service.GetConn()
	defer engine.Close()

	if sign, err = util.MakeSign(engine, data); err != nil {
		return err
	}

	if data["sign"] == "" || sign != data["sign"] {
		fmt.Println("传过来的：" + data["sign"])
		fmt.Println("自己生成的：" + sign)
		return errors.New("签名sign不正确")
	}

	return err
}

// 标准支付接口签名校验
func CheckSignForStandardPay(next echo.HandlerFunc) echo.HandlerFunc {

	return func(c echo.Context) error {
		var (
			err  error
			sign string
			list = make(map[string]string)
		)
		list = util.GetBodyParams(c)
		glog.Info("请求参数:", list)

		// app支付签名规则不一样，只需要特定字段参与签名，新增支付宝小程序类型17，18-百度小程序
		if list["trans_type"] == "10" || list["trans_type"] == "11" || list["trans_type"] == "17" || list["trans_type"] == "18" {
			list = map[string]string{
				"app_id":     list["app_id"],
				"notify_url": list["notify_url"],
				"order_name": list["order_name"],
				"order_no":   list["order_no"],
				"pay_amount": list["pay_amount"],
				"pay_total":  list["pay_total"],
				"timestamp":  list["timestamp"],
				"sign":       list["sign"],
			}
		}

		// 签名
		if sign, err = util.MakeSignForStandardPay(list); err != nil {
			glog.Error(err.Error(), "CheckSignForStandardPay请求参数:", list)
			return service.ReturnJson(c, http.StatusBadRequest, err.Error())
		}
		if list["sign"] == "" || sign != list["sign"] {
			glog.Error("签名sign不正确", "CheckSignForStandardPay请求参数:", list, sign)
			return service.ReturnJson(c, http.StatusBadRequest, "签名sign不正确")
		}

		return next(c)
	}
}
