package dto

import "pay-center/proto/pay"

type Pay struct {
	//回调地址
	NotifyURL string `json:"notify_url" validate:"required"`
	//订单名称
	OrderName string `json:"order_name" validate:"required"`
	ProductId string `json:"product_id"`
	//商品描述
	ProductDesc string `json:"product_desc"`
	//订单号
	OrderNo string `json:"order_no" validate:"required"`
	//实付金额,单位：分
	PayAmount int64 `json:"pay_amount" validate:"required"`
	//总金额,单位：分
	PayTotal int64 `json:"pay_total" validate:"required"`
	//签名，互联网医疗和阿闻只有部分字段参与签名，如下：app_id=2&notify_url=https://123&order_name=test&order_no=123&pay_amount=1&pay_total=1&timestamp=1234567891234&secret=5fBgKs5UYD2t11PUzLxQqrRIBDwAwggEKAoIBAQDL2qWFfEVHQ8BAf8EBAMCBs
	Sign string `json:"sign" validate:"required"`
	//1：阿闻，2：子龙，3：R1，4：互联网,，5：SAAS
	AppId int32 `json:"app_id" validate:"required"`
	//1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准） 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付
	TransType int32 `json:"trans_type" validate:"required"`
	//1、竖屏B扫C时：传标准终端绑定接口返回的dyTermNo
	//2、B扫C（标准时）：传电银内部终端号
	TrmId string `json:"trm_id"`
	//微信 JSAPI 支付时必传
	OpenId string `json:"open_id"`
	//子商户公众账号 ID
	SubAppId string `json:"sub_app_id"`
	//1、竖屏B扫C时：传机具编号（tsn）
	//2、B扫C（标准时）：智能 POS 终端的机具编号
	TrmSn string `json:"trm_sn"`
	////交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"order_pay_type"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extend_info"`
	//优惠金额,单位：分
	Discount int64 `json:"discount"`
	//付款码,用户用银联支付宝、微信生成的付款码
	BarCode string `json:"bar_code"`
	//商户号 (trans_type 为12时必传)
	MerchantId string `json:"merchant_id"`
	ClientIP   string `json:"client_ip"`
	//订单有限时间（分）
	ValidTime int32 `json:"valid_time"`
	//时间戳，用于签名（毫秒）
	Timestamp int64 `json:"timestamp"  validate:"required"`
	//前台回调地址  网银支付参数
	FrontUrl string `json:"front_url"`
	//应用渠道标识  App-Android  App-iOS H5 web   网银支付参数
	ChannelType string `json:"channel_type"`
	//产品类型  ENTERPRISE_BANK(企业网银)  QUICK_PAY(快捷) PERSONAL_BANK(个人网银)  COMPLEX_BANK(综合收银)  网银支付参数
	ProdType string `json:"prod_type"`
	//接口类型  0：跳转网页支付 1：直接接口支付  网银支付参数
	InterfaceType string `json:"interface_type"`
	//卡类型  0：借记卡  1：贷记卡  接口类型为直接接口支付时必传   网银支付参数
	CardType string `json:"card_type"`
	//银行编码 接口类型为直接接口支付时必传   网银支付参数
	BankCode string `json:"bank_code"`
}

// 回调通知
type PayCallback struct {
	pay.StandardPayResponse
	//扩展信息，原样返回
	ExtendInfo string `json:"extend_info"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `json:"app_id" validate:"required"`
	// 毫秒时间戳
	Timestamp string `json:"timestamp"`
	// 签名
	Sign string `json:"sign"`
}

// 回调通知    通知可能增加字段，验证签名时必须支持增加的扩展字段
type AsyncCallback struct {
	//支付中心流水号
	TradeNo string `json:"tradeNo"`
	//商户流水号
	OutTradeNo string `json:"outTradeNo"`
	// 交易流水号(电银流水号)
	TransactionNo string `json:"transactionNo"`
	//订单日期  格式：YYYYMMDD
	OrderTime string `json:"orderTime"`
	//优惠   单位分
	Discount string `json:"discount"`
	//实付价格   单位分
	PayPrice string `json:"payPrice"`
	//总价格      单位分
	TotalPrice string `json:"totalPrice"`
	//商品编号
	ProductId string `json:"productId"`
	//商品名称
	ProductName string `json:"productName"`
	//商品描述
	ProductDesc string `json:"productDesc"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl"`
	//客户端 IP
	ClientIp string `json:"clientIp"`
	//添加时间
	AddTime string `json:"addTime"`
	//支付时间
	PayTime string `json:"payTime"`
	//商户号
	MerchantId string `json:"merchantId"`
	//扩展信息，原样返回
	ExtendInfo string `json:"extendInfo"`
	//扣款通道返回的流水号
	ChannelNo string `json:"channelNo"`
	//商户订单号
	OrderId string `json:"orderId"`
	//支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
	PayType string `json:"payType"`
	// 毫秒时间戳
	Timestamp string `json:"timestamp"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	AppId string `json:"app_id"`
	//1：成功  3：失败
	Result string `json:"result"`
}

// 退款回调说明文档
type AsyncRefundCallback struct {
	// 退款状态（退款状态 0：未退款 1：退款成功 2：退款处理中 3：退款失败）
	RspCode int `json:"rspCode"`
	// 退款说明
	RspMessage string `json:"rspMessage"`
	// 退款订单号
	RefundId string `json:"refundId"`
	// 交易流水号
	TransactionNo string `json:"transactionNo"`
	// 回调地址
	CallbackUrl string `json:"callbackUrl"`
	// 客户端 IP
	ClientIP string `json:"clientIP"`
	// 退款额
	RefundAmt int `json:"refundAmt"`
	// 商户私有域：交易返回时原样返回给商户网站，给商户备用
	BackParam string `json:"backParam"`
	// 扩展信息：预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo"`
	// 商户退款订单号
	RefundOrderId string `json:"refundOrderId"`
	// 时间戳，验签用
	Timestamp string `json:"timestamp"`
	//1：阿闻，2：子龙，3：R1，4：互联网,5：saas
	AppId string `json:"app_id"`
	// 签名
	Sign string `json:"sign"`
}

type PayRefund struct {
	//1：阿闻，2：子龙，3：R1，4：互联网,5：saas
	AppId int32 `json:"app_id" validate:"required"`
	//退款金额（分）
	RefundAmount int64 `json:"refund_amount" validate:"required"`
	//支付中心订单号
	TradeNo string `json:"trade_no" validate:"required"`
	//商户退款订单号
	RefundId string `json:"refund_id"`
	//签名
	Sign string `json:"sign" validate:"required"`
	//客户端 IP
	ClientIP string `json:"client_ip"`
	//后台回调地址
	NotifyUrl string `json:"notify_url" validate:"required"`
	//商户私有域
	BackParam string `json:"back_param"`
	//扩展信息
	ExtendInfo string `json:"extend_info"`
}
type PayClose struct {
	//支付中心订单号
	TradeNo string `json:"trade_no" validate:"required"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `json:"app_id" validate:"required"`
	//签名
	Sign string `json:"sign" form:"sign" validate:"required"`
	//时间戳，用于签名（毫秒）
	Timestamp int64 `json:"timestamp"  validate:"required"`
}
type StandardPayStatus struct {
	//支付中心订单号
	TradeNo string `json:"trade_no" validate:"required"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `json:"app_id" validate:"required"`
	//1 支付 2 退款
	Type int32 `json:"type" validate:"required"`
	//签名
	Sign string `json:"sign" form:"sign" validate:"required"`
	//时间戳，用于签名（毫秒）
	Timestamp int64 `json:"timestamp"  validate:"required"`
}

type BaseResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

type PayResponse struct {
	BaseResponse
	Data *pay.StandardPayResponse `json:"data"`
}
type PayRefundResponse struct {
	BaseResponse
	Data *pay.StandardPayRefundResponse `json:"data"`
}
type PayStatusResponse struct {
	BaseResponse
	Data *pay.StandardPayStatusResponse `json:"data"`
}

type StandardPayConfig struct {
	BaseResponse
	Data struct {
		// app支付配置信息
		App struct {
			OpenWx     int32 `json:"open_wx"`     // 微信支付开关，默认0-关闭，1-开启
			OpenAlipay int32 `json:"open_alipay"` // 支付宝支付开关，默认0-关闭，1-开启
		} `json:"app"`
	} `json:"data"`
}
