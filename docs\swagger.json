{"swagger": "2.0", "info": {"description": "这里是描述", "title": "pay_echo项目接口文档", "contact": {}, "version": "1.0"}, "host": "localhost:7035", "paths": {"/callback-pay": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付回调说明", "parameters": [{"description": " ", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AsyncCallback"}}], "responses": {}}}, "/callback-refund": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付退款回调说明", "parameters": [{"description": " ", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AsyncRefundCallback"}}], "responses": {}}}, "/pay/account/pay/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "获取支付方式", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.QueryPwdRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.PayListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.PayListResponse"}}}}}, "/pay/account/pwd/check-code": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "检查短信验证码是否正确", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CheckCodeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.CheckCodeResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.CheckCodeResponse"}}}}}, "/pay/account/pwd/query": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "查询是否设置过密码", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.QueryPwdRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.QueryPwdResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.QueryPwdResponse"}}}}}, "/pay/account/pwd/sendcode": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "发送短信验证码", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.QueryPwdRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/account/pwd/set": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "设置余额支付密码-首次", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.SetPwdRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/account/pwd/update-mobile": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "通过手机号修改密码", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.UpdateByMobileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/account/pwd/update-pwd": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付设置"], "summary": "通过原密码修改密码", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.UpdateByPwdRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/card/balance": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "获取储蓄卡余额", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.GetCardsBalanceReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.GetCardsBalanceRes"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.GetCardsBalanceRes"}}}}}, "/pay/card/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "获取储蓄卡列表", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.GetCardsReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.GetCardsRes"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.GetCardsRes"}}}}}, "/pay/card/order-record": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "储蓄卡订单交易记录", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CardOrderRecordsReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.CardOrderRecordsRes"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.CardOrderRecordsRes"}}}}}, "/pay/card/pay": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "储蓄卡支付", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CardPayReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/card/pay-type": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "获取支付方式（默认储蓄卡支付）", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CheckPayTypeReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.CheckPayTypeRes"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.CheckPayTypeRes"}}}}}, "/pay/card/record": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "储蓄卡交易流水明细", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CardRecordsReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.CardRecordsRes"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.CardRecordsRes"}}}}}, "/pay/card/refund": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["储值卡"], "summary": "储蓄卡退款", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.CardsRefundReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/dy-pay-info": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付订单详情", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.PayInfoQueryRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.PayInfoQueryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.PayInfoQueryResponse"}}}}}, "/pay/dy-refund-info": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准退款订单详情", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.DYRefundInfoRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.DYRefundInfoResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.DYRefundInfoResponse"}}}}}, "/pay/dy/dypay": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付"], "summary": "电银支付", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.DyPay"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.DYPayResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.PayForB2CResponse"}}}}}, "/pay/dy/payb2c": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付"], "summary": "支付电银B2C", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.PayForB2CRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.PayForB2CResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.PayForB2CResponse"}}}}}, "/pay/dy/querypaystatus": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付"], "summary": "支付状态查询", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.QueryPayStatusRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.QueryPayStatusResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.QueryPayStatusResponse"}}}}}, "/pay/dy/querysts": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["标准终端"], "summary": "标准终端状态查询", "parameters": [{"type": "string", "description": "机具编号", "name": "tsn", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.QueryStsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.QueryStsResponse"}}}}}, "/pay/dy/stdbinding": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["标准终端"], "summary": "标准终端绑定", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.StdBindingRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.StdBindingResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.StdBindingResponse"}}}}}, "/pay/dy/stdunbinding": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["标准终端"], "summary": "标准终端解绑", "parameters": [{"type": "string", "description": "机具编号", "name": "tsn", "in": "query", "required": true}, {"type": "string", "description": "默认3：竖屏B扫C，12：B扫C（标准）", "name": "payType", "in": "query"}, {"type": "string", "description": "电银商户号", "name": "dyMchNo", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/pay-api/config": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付开关配置（目前app端用）", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dto.StandardPayConfig"}}}}}, "/pay/pay-api/pay": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.Pay"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dto.PayResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/dto.PayResponse"}}}}}, "/pay/pay-api/pay/close": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "支付关闭", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.PayClose"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.BaseResponse"}}}}}, "/pay/pay-api/pay/refund": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "标准支付退款", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.PayRefund"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dto.PayRefundResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/dto.PayRefundResponse"}}}}}, "/pay/pay-api/pay/status": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付中心"], "summary": "支付状态", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.StandardPayStatus"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dto.PayStatusResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/dto.PayStatusResponse"}}}}}, "/pay/refund": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["支付"], "summary": "退款", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.RefundDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.PayRefundResponse"}}}}}, "/refund/dy/newrefund": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["退款"], "summary": "新退款", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.NewRefund"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.NewRefundResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.NewRefundResponse"}}}}}, "/refund/dy/queryrefundstatus": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["退款"], "summary": "退款状态查询", "parameters": [{"description": " ", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pay.QueryRefundStatusRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/pay.QueryRefundStatusResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/pay.QueryRefundStatusResponse"}}}}}}, "definitions": {"dto.AsyncCallback": {"type": "object", "properties": {"addTime": {"description": "添加时间", "type": "string"}, "app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网", "type": "string"}, "channelNo": {"description": "扣款通道返回的流水号", "type": "string"}, "clientIp": {"description": "客户端 IP", "type": "string"}, "discount": {"description": "优惠   单位分", "type": "string"}, "extendInfo": {"description": "扩展信息，原样返回", "type": "string"}, "merchantId": {"description": "商户号", "type": "string"}, "offlineNotifyUrl": {"description": "后台回调地址", "type": "string"}, "orderId": {"description": "商户订单号", "type": "string"}, "orderTime": {"description": "订单日期  格式：YYYYMMDD", "type": "string"}, "outTradeNo": {"description": "商户流水号", "type": "string"}, "payPrice": {"description": "实付价格   单位分", "type": "string"}, "payTime": {"description": "支付时间", "type": "string"}, "payType": {"description": "支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）", "type": "string"}, "productDesc": {"description": "商品描述", "type": "string"}, "productId": {"description": "商品编号", "type": "string"}, "productName": {"description": "商品名称", "type": "string"}, "result": {"description": "1：成功  3：失败", "type": "string"}, "timestamp": {"description": "毫秒时间戳", "type": "string"}, "totalPrice": {"description": "总价格      单位分", "type": "string"}, "tradeNo": {"description": "支付中心流水号", "type": "string"}, "transactionNo": {"description": "交易流水号(电银流水号)", "type": "string"}}}, "dto.AsyncRefundCallback": {"type": "object", "properties": {"app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网,5：saas", "type": "string"}, "backParam": {"description": "商户私有域：交易返回时原样返回给商户网站，给商户备用", "type": "string"}, "callbackUrl": {"description": "回调地址", "type": "string"}, "clientIP": {"description": "客户端 IP", "type": "string"}, "extendInfo": {"description": "扩展信息：预留字段，JSON 格式", "type": "string"}, "refundAmt": {"description": "退款额", "type": "integer"}, "refundId": {"description": "退款订单号", "type": "string"}, "refundOrderId": {"description": "商户退款订单号", "type": "string"}, "rspCode": {"description": "退款状态（退款状态 0：未退款 1：退款成功 2：退款处理中 3：退款失败）", "type": "integer"}, "rspMessage": {"description": "退款说明", "type": "string"}, "sign": {"description": "签名", "type": "string"}, "timestamp": {"description": "时间戳，验签用", "type": "string"}, "transactionNo": {"description": "交易流水号", "type": "string"}}}, "dto.DyPay": {"type": "object", "properties": {"bar_code": {"description": "付款码,用户用银联支付宝、微信生成的付款码", "type": "string"}, "channel_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "discount": {"description": "优惠金额,单位：分", "type": "integer"}, "merchant_id": {"description": "商户号", "type": "string"}, "notify_url": {"description": "回调地址", "type": "string"}, "order_name": {"description": "订单名称", "type": "string"}, "order_no": {"description": "订单号", "type": "string"}, "pay_amount": {"description": "实付金额,单位：分", "type": "integer"}, "pay_total": {"description": "总金额,单位：分", "type": "integer"}, "pay_type": {"description": "支付方式 1：微信 2：支付宝 3: 银联", "type": "integer"}, "sign": {"description": "签名", "type": "string"}, "trancde": {"description": "交易码,P00：默认值，支持微信、支付宝、云闪付、电银支付等；CSU01：仅支持云闪付（老接口兼容性保留值）；", "type": "string"}, "trm_id": {"description": "终端号，传标准终端绑定接口返回的dyTermNo", "type": "string"}, "trm_sn": {"description": "传机具编号（tsn）", "type": "string"}}}, "dto.NewRefund": {"type": "object", "properties": {"head_base": {"description": "请求头信息", "allOf": [{"$ref": "#/definitions/pay.ScanHeadBase"}]}, "mer_order_no": {"description": "商户订单号(原交易)", "type": "string"}, "notify_url": {"type": "string"}, "refund_amount": {"description": "退款金额  单位：分", "type": "string"}, "refund_remark": {"description": "退款备注", "type": "string"}, "trancde": {"description": "交易码：非银联时（微信，支付宝）：P02 银联：CSU03", "type": "string"}}}, "dto.Pay": {"type": "object", "required": ["app_id", "notify_url", "order_name", "order_no", "pay_amount", "pay_total", "sign", "timestamp", "trans_type"], "properties": {"app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网,，5：SAAS", "type": "integer"}, "bank_code": {"description": "银行编码 接口类型为直接接口支付时必传   网银支付参数", "type": "string"}, "bar_code": {"description": "付款码,用户用银联支付宝、微信生成的付款码", "type": "string"}, "card_type": {"description": "卡类型  0：借记卡  1：贷记卡  接口类型为直接接口支付时必传   网银支付参数", "type": "string"}, "channel_type": {"description": "应用渠道标识  App-Android  App-iOS H5 web   网银支付参数", "type": "string"}, "client_ip": {"type": "string"}, "discount": {"description": "优惠金额,单位：分", "type": "integer"}, "extend_info": {"description": "扩展信息 预留字段，JSON 格式", "type": "string"}, "front_url": {"description": "前台回调地址  网银支付参数", "type": "string"}, "interface_type": {"description": "接口类型  0：跳转网页支付 1：直接接口支付  网银支付参数", "type": "string"}, "merchant_id": {"description": "商户号 (trans_type 为12时必传)", "type": "string"}, "notify_url": {"description": "回调地址", "type": "string"}, "open_id": {"description": "微信 JSAPI 支付时必传", "type": "string"}, "order_name": {"description": "订单名称", "type": "string"}, "order_no": {"description": "订单号", "type": "string"}, "order_pay_type": {"description": "//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)", "type": "string"}, "pay_amount": {"description": "实付金额,单位：分", "type": "integer"}, "pay_total": {"description": "总金额,单位：分", "type": "integer"}, "prod_type": {"description": "产品类型  ENTERPRISE_BANK(企业网银)  QUICK_PAY(快捷) PERSONAL_BANK(个人网银)  COMPLEX_BANK(综合收银)  网银支付参数", "type": "string"}, "product_desc": {"description": "商品描述", "type": "string"}, "product_id": {"type": "string"}, "sign": {"description": "签名，互联网医疗和阿闻只有部分字段参与签名，如下：app_id=2&notify_url=https://123&order_name=test&order_no=123&pay_amount=1&pay_total=1&timestamp=*************&secret=5fBgKs5UYD2t11PUzLxQqrRIBDwAwggEKAoIBAQDL2qWFfEVHQ8BAf8EBAMCBs", "type": "string"}, "sub_app_id": {"description": "子商户公众账号 ID", "type": "string"}, "timestamp": {"description": "时间戳，用于签名（毫秒）", "type": "integer"}, "trans_type": {"description": "1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准） 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付", "type": "integer"}, "trm_id": {"description": "1、竖屏B扫C时：传标准终端绑定接口返回的dyTermNo\n2、B扫C（标准时）：传电银内部终端号", "type": "string"}, "trm_sn": {"description": "1、竖屏B扫C时：传机具编号（tsn）\n2、B扫C（标准时）：智能 POS 终端的机具编号", "type": "string"}, "valid_time": {"description": "订单有限时间（分）", "type": "integer"}}}, "dto.PayClose": {"type": "object", "required": ["app_id", "sign", "timestamp", "trade_no"], "properties": {"app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "sign": {"description": "签名", "type": "string"}, "timestamp": {"description": "时间戳，用于签名（毫秒）", "type": "integer"}, "trade_no": {"description": "支付中心订单号", "type": "string"}}}, "dto.PayRefund": {"type": "object", "required": ["app_id", "notify_url", "refund_amount", "sign", "trade_no"], "properties": {"app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网,5：saas", "type": "integer"}, "back_param": {"description": "商户私有域", "type": "string"}, "client_ip": {"description": "客户端 IP", "type": "string"}, "extend_info": {"description": "扩展信息", "type": "string"}, "notify_url": {"description": "后台回调地址", "type": "string"}, "refund_amount": {"description": "退款金额（分）", "type": "integer"}, "refund_id": {"description": "商户退款订单号", "type": "string"}, "sign": {"description": "签名", "type": "string"}, "trade_no": {"description": "支付中心订单号", "type": "string"}}}, "dto.PayRefundResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.StandardPayRefundResponse"}, "message": {"type": "string"}}}, "dto.PayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.StandardPayResponse"}, "message": {"type": "string"}}}, "dto.PayStatusResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.StandardPayStatusResponse"}, "message": {"type": "string"}}}, "dto.RefundDto": {"type": "object", "required": ["clientIP", "merchantId", "refundAmt", "sign", "tradeNo"], "properties": {"appId": {"description": "渠道appid", "type": "integer"}, "backParam": {"description": "商户私有域", "type": "string"}, "callbackUrl": {"description": "后台回调地址", "type": "string"}, "clientIP": {"description": "客户端 IP", "type": "string"}, "extendInfo": {"description": "扩展信息", "type": "string"}, "merchantId": {"description": "商户号", "type": "string", "maxLength": 32}, "refundAmt": {"description": "退款金额", "type": "integer"}, "refundId": {"description": "商户退款订单号", "type": "string"}, "refundType": {"type": "integer"}, "sign": {"description": "签名", "type": "string", "maxLength": 32}, "tradeNo": {"description": "支付中心流水号", "type": "string", "maxLength": 32}}}, "dto.StandardPayConfig": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"app": {"description": "app支付配置信息", "type": "object", "properties": {"open_alipay": {"description": "支付宝支付开关，默认0-关闭，1-开启", "type": "integer"}, "open_wx": {"description": "微信支付开关，默认0-关闭，1-开启", "type": "integer"}}}}}, "message": {"type": "string"}}}, "dto.StandardPayStatus": {"type": "object", "required": ["app_id", "sign", "timestamp", "trade_no", "type"], "properties": {"app_id": {"description": "1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "sign": {"description": "签名", "type": "string"}, "timestamp": {"description": "时间戳，用于签名（毫秒）", "type": "integer"}, "trade_no": {"description": "支付中心订单号", "type": "string"}, "type": {"description": "1 支付 2 退款", "type": "integer"}}}, "pay.BaseResponse": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer"}, "error": {"description": "错误信息", "type": "string"}, "message": {"description": "消息", "type": "string"}}}, "pay.BizInfoTpData": {"type": "object", "properties": {"appKey": {"type": "string"}, "dealId": {"type": "string"}, "totalAmount": {"type": "string"}, "tpOrderId": {"type": "string"}}}, "pay.CardData": {"type": "object", "properties": {"card_id": {"type": "integer"}, "card_name": {"description": "卡名称", "type": "string"}, "card_number": {"description": "卡号", "type": "string"}, "chained": {"description": "是否连锁 1：是 0：否", "type": "integer"}, "corpus_balance": {"description": "本金余额(单位元)", "type": "number"}, "create_time": {"description": "创建日期（根据有效期判断卡是否过期）", "type": "string"}, "expiry_date": {"description": "有效期（根据有效期判断卡是否过期）", "type": "string"}, "is_available": {"description": "是否可用 1：是 0：否", "type": "integer"}, "is_checked": {"description": "是否选中 1：是 0：否", "type": "integer"}, "org_id": {"description": "开卡门店(财务编码)", "type": "string"}, "org_name": {"description": "开卡门店名", "type": "string"}, "pay_amount": {"description": "选中的卡支付金额(单位分)", "type": "integer"}, "present_balance": {"description": "赠送余额(单位元)", "type": "number"}}}, "pay.CardOrderRecordData": {"type": "object", "properties": {"card_id": {"description": "卡id", "type": "integer"}, "card_name": {"description": "卡名称", "type": "string"}, "card_number": {"description": "卡号", "type": "string"}, "corpus_change": {"description": "本金发生金额", "type": "number"}, "occur_org_id": {"description": "发生地点（医院机构Id）", "type": "string"}, "occur_time": {"description": "发生时间", "type": "string"}, "operate_type": {"description": "交易类型,3：消费扣款 4：消费退款", "type": "string"}}}, "pay.CardOrderRecordsReq": {"type": "object", "properties": {"operate_type": {"description": "交易类型3：消费扣款 4：消费退款", "type": "integer"}, "order_sn": {"description": "订单号", "type": "string"}}}, "pay.CardOrderRecordsRes": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pay.CardOrderRecordData"}}, "message": {"type": "string"}}}, "pay.CardPayReq": {"type": "object", "properties": {"cards": {"description": "是否连锁 1：是 0：否", "type": "array", "items": {"$ref": "#/definitions/pay.CardsPayDetails"}}, "finance_code": {"description": "财务编码", "type": "string"}, "mobile": {"description": "手机号", "type": "string"}, "order_sn": {"description": "交易流水号（调用支付中心返回）", "type": "string"}, "order_source": {"description": "订单来源:01商城，02阿闻到家，03挂号", "type": "string"}, "order_type": {"description": "交易类型", "type": "string"}, "pay_pwd": {"description": "支付密码", "type": "string"}, "pay_type": {"description": "支付方式 0支付密码支付  1短信验证支付", "type": "integer"}, "present_money": {"description": "赠送金额(单元分)", "type": "integer"}, "scrm_id": {"description": "scrm_id", "type": "string"}, "sms_code": {"description": "短信验证码", "type": "string"}}}, "pay.CardRecordData": {"type": "object", "properties": {"card_id": {"type": "integer"}, "card_number": {"description": "卡号", "type": "string"}, "corpus_after_change": {"description": "本金余额", "type": "number"}, "corpus_before_change": {"description": "赠送余额(单位元)", "type": "number"}, "corpus_change": {"description": "本金部分发生金额", "type": "number"}, "occur_org_id": {"description": "发生地点（财务编码）", "type": "string"}, "occur_org_name": {"description": "发生地点（医院机构名称）", "type": "string"}, "occur_time": {"description": "发生时间", "type": "string"}, "operate_type": {"description": "交易类型 1：开卡 ， 2：充值  3：消费扣款  4：消费退款，6：客户退卡", "type": "integer"}, "pay_number": {"description": "第三方流水号", "type": "string"}, "present_after_change": {"description": "赠送部分余额", "type": "number"}, "present_before_change": {"description": "赠送部分发生前金额", "type": "number"}, "present_change": {"description": "赠送部分发生金额", "type": "number"}, "record_number": {"description": "流水号（子龙)", "type": "string"}}}, "pay.CardRecordsReq": {"type": "object", "properties": {"card_id": {"type": "integer"}, "page_index": {"type": "integer"}, "page_size": {"type": "integer"}}}, "pay.CardRecordsRes": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pay.CardRecordData"}}, "data_count": {"type": "integer"}, "message": {"type": "string"}}}, "pay.CardsBalanceData": {"type": "object", "properties": {"available_balance": {"description": "可用余额（本金-（本日充值金额0.3））", "type": "string"}, "balance": {"description": "余额（本金+赠送金额)", "type": "string"}, "gift_amount": {"description": "赠送金额", "type": "string"}}}, "pay.CardsPayDetails": {"type": "object", "properties": {"card_id": {"type": "integer"}, "pay_money": {"description": "支付金额(单位元)", "type": "integer"}}}, "pay.CardsRefundReq": {"type": "object", "properties": {"finance_code": {"description": "财务编码", "type": "string"}, "order_sn": {"description": "订单号", "type": "string"}, "order_source": {"description": "订单来源:01商城，02阿闻到家，03挂号", "type": "string"}, "refund_money": {"description": "退款金额(单位分)", "type": "integer"}, "refund_order_sn": {"description": "退款订单号", "type": "string"}, "scrm_id": {"description": "scrm_id", "type": "string"}}}, "pay.CheckCodeRequest": {"type": "object", "properties": {"code": {"description": "验证码", "type": "string"}, "mobile": {"description": "手机号", "type": "string"}}}, "pay.CheckCodeResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "is_true": {"description": "验证码是否正确 1是 0否", "type": "integer"}, "message": {"type": "string"}}}, "pay.CheckPayTypeReq": {"type": "object", "properties": {"finance_code": {"description": "财务编码", "type": "string"}, "order_source": {"description": "订单来源:01商城，02阿闻到家，03挂号", "type": "string"}, "pay_money": {"description": "支付金额(单位分)", "type": "integer"}, "scrm_id": {"description": "scrm_id", "type": "string"}}}, "pay.CheckPayTypeRes": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.PayTypeData"}, "message": {"type": "string"}}}, "pay.DYPayResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "message": {"type": "string"}, "order_no": {"description": "商户订单号", "type": "string"}, "pay_amount": {"description": "订单总金额 单位：分", "type": "string"}, "pay_no": {"description": "电银流水号", "type": "string"}, "pay_result": {"description": "支付结果 S：成功 R：正在执行 F：失败", "type": "string"}, "pay_time": {"description": "支付时间", "type": "string"}, "trade_no": {"description": "支付中心订单号", "type": "string"}}}, "pay.DYRefundInfoRequest": {"type": "object", "properties": {"merchant_id": {"type": "string"}, "trade_no": {"type": "string"}}}, "pay.DYRefundInfoResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.DYRefundInfoResponse_DYRefundInfoData"}, "message": {"type": "string"}}}, "pay.DYRefundInfoResponse_DYRefundInfoData": {"type": "object", "properties": {"fee_amt": {"description": "退款手续费(返回空就是手续费没结算)", "type": "string"}, "order_Id": {"description": "支付订单号", "type": "string"}, "refund_state": {"description": "退款状态  S:退款成功  F:退款失败  P:退款处理中", "type": "string"}, "trade_no": {"description": "退款订单号", "type": "string"}}}, "pay.GetCardsBalanceReq": {"type": "object", "properties": {"scrm_id": {"type": "string"}}}, "pay.GetCardsBalanceRes": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.CardsBalanceData"}, "message": {"type": "string"}}}, "pay.GetCardsData": {"type": "object", "properties": {"cards": {"description": "财务编码", "type": "array", "items": {"$ref": "#/definitions/pay.CardData"}}, "is_pay": {"description": "是否可以支付 1:是,0:否", "type": "integer"}}}, "pay.GetCardsReq": {"type": "object", "properties": {"channel_id": {"description": "渠道Id 阿闻：1，阿闻电商：5", "type": "integer"}, "finance_code": {"description": "财务编码", "type": "string"}, "pay_money": {"description": "订单支付金额（单位分）", "type": "integer"}, "scrm_id": {"type": "string"}}}, "pay.GetCardsRes": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.GetCardsData"}, "message": {"type": "string"}}}, "pay.NewRefundResponse": {"type": "object", "properties": {"actual_refund_amount": {"description": "实际退款金额 单位：分", "type": "string"}, "code": {"description": "返回码", "type": "integer"}, "mer_order_no": {"description": "商户订单号", "type": "string"}, "mer_refund_order_no": {"description": "商户退款单号", "type": "string"}, "message": {"type": "string"}, "refund_amount": {"description": "退款金额 单位：分", "type": "string"}, "refund_no": {"description": "退款流水号", "type": "string"}, "refund_result": {"description": "支付结果 S：成功 R：正在执行 F：失败", "type": "string"}, "refund_time": {"description": "退款时间", "type": "string"}}}, "pay.PayForB2CRequest": {"type": "object", "properties": {"NotifyUrl": {"description": "后台回调地址（支付中心回调电商）", "type": "string"}, "app_id": {"description": "应用id，1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "bar_code": {"description": "付款码", "type": "string"}, "client_ip": {"description": "ip", "type": "string"}, "discount": {"description": "优惠", "type": "integer"}, "extend_info": {"description": "扩展字段", "type": "string"}, "head_base": {"description": "扫码请求头信息", "allOf": [{"$ref": "#/definitions/pay.ScanHeadBase"}]}, "location": {"type": "string"}, "mer_order_no": {"description": "订单号", "type": "string"}, "order_desc": {"description": "订单描述", "type": "string"}, "order_name": {"description": "订单名称 银联时否", "type": "string"}, "out_order_no": {"description": "外部订单号", "type": "string"}, "pay_amount": {"description": "实际支付金额", "type": "integer"}, "pay_type": {"description": "支付方式 1：微信 2：支付宝 3: 银联", "type": "integer"}, "termType": {"description": "终端类型，默认15", "type": "string"}, "total_amount": {"description": "订单总金额", "type": "integer"}, "undiscountable_amount": {"description": "不参与优惠金额", "type": "string"}, "validNum": {"description": "订单有效期单位 结合单位一起使用", "type": "string"}, "validUnit": {"description": "订单有效期单位 00-分 01-小时 02-日 03-月", "type": "string"}}}, "pay.PayForB2CResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "mer_order_no": {"description": "商户订单号", "type": "string"}, "message": {"type": "string"}, "pay_amount": {"description": "订单总金额 单位：分", "type": "string"}, "pay_no": {"description": "电银流水号", "type": "string"}, "pay_result": {"description": "支付结果 S：成功 R：正在执行 F：失败", "type": "string"}, "pay_time": {"description": "支付时间", "type": "string"}, "trade_no": {"description": "支付中心流水号", "type": "string"}}}, "pay.PayInfoQueryRequest": {"type": "object", "properties": {"app_id": {"description": "应用id，1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "merchant_id": {"description": "商户号", "type": "string"}, "order_id": {"description": "商户订单号", "type": "string"}, "pay_type": {"description": "支付方式", "type": "integer"}, "product_desc": {"description": "商品描述", "type": "string"}}}, "pay.PayInfoQueryResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.PayInfoQueryResponsePayInfo"}, "message": {"type": "string"}}}, "pay.PayInfoQueryResponsePayInfo": {"type": "object", "properties": {"fee_amt": {"description": "交易手续费(返回空就是手续费没结算)", "type": "string"}, "order_id": {"description": "原交易订单号", "type": "string"}, "order_time": {"description": "订单创建时间", "type": "string"}, "pay_time": {"description": "支付完成时间", "type": "string"}, "trade_no": {"description": "支付流水号", "type": "string"}, "trans_amt": {"description": "交易金额", "type": "integer"}, "trans_state": {"description": "交易状态", "type": "string"}}}, "pay.PayListResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pay.PayMethod"}}, "message": {"type": "string"}}}, "pay.PayMethod": {"type": "object", "properties": {"id": {"type": "integer"}, "is_open": {"description": "是否开启", "type": "integer"}, "pay_method": {"description": "支付方式", "type": "string"}}}, "pay.PayRefundResponse": {"type": "object", "properties": {"baiduData": {"$ref": "#/definitions/pay.PayRefundReturnBaiduData"}, "code": {"type": "integer"}, "data": {"$ref": "#/definitions/pay.PayRefundReturnData"}, "message": {"type": "string"}}}, "pay.PayRefundReturnBaiduData": {"type": "object", "properties": {"refundBatchId": {"description": "平台退款批次号", "type": "string"}, "refundPayMoney": {"description": "平台可退退款金额【分为单位】", "type": "integer"}}}, "pay.PayRefundReturnData": {"type": "object", "properties": {"backParam": {"description": "商户私有域：交易返回时原样返回给商户网站，给商户备用", "type": "string"}, "callbackUrl": {"description": "后台回调地址", "type": "string"}, "clientIP": {"description": "客户端 IP", "type": "string"}, "extendInfo": {"description": "扩展信息：预留字段，JSON 格式", "type": "string"}, "merchantId": {"description": "商户号", "type": "string"}, "refundAmt": {"description": "退款金额，以分为单位", "type": "string"}, "refundId": {"description": "退款订单号", "type": "string"}, "rspCode": {"description": "退款状态 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败", "type": "string"}, "rspMessage": {"description": "返回信息", "type": "string"}, "sign": {"description": "签名", "type": "string"}, "transactionNo": {"description": "交易流水号", "type": "string"}}}, "pay.PayTypeData": {"type": "object", "properties": {"is_jump_cashier": {"description": "是否跳转收银台", "type": "boolean"}, "pay_type": {"description": "支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付", "type": "integer"}}}, "pay.QueryPayStatusRequest": {"type": "object", "properties": {"app_id": {"description": "应用id，1：阿闻，2：子龙，3：R1，4：互联网", "type": "integer"}, "head_base": {"description": "请求头信息", "allOf": [{"$ref": "#/definitions/pay.ScanHeadBase"}]}, "trade_no": {"description": "支付中心流水号", "type": "string"}, "trancde": {"description": "交易码，默认值：PF0 银联云闪付：QRY1 动码查询：PF420 云闪付动码：QRY4", "type": "string"}}}, "pay.QueryPayStatusResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "message": {"type": "string"}, "order_no": {"description": "订单号", "type": "string"}, "pay_amount": {"description": "订单总金额 单位：分", "type": "string"}, "pay_no": {"description": "电银流水号", "type": "string"}, "pay_result": {"description": "支付结果 I：待支付 S：成功 R：正在执行 F：失败 T：成功有退款 C：已撤销 O：交易关闭", "type": "string"}, "pay_time": {"description": "支付时间", "type": "string"}, "trade_no": {"description": "商户订单号", "type": "string"}}}, "pay.QueryPwdRequest": {"type": "object", "properties": {"mobile": {"type": "string"}, "order_sn": {"type": "string"}, "type": {"type": "integer"}}}, "pay.QueryPwdResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "is_pwd": {"description": "1设置过  0未设置", "type": "integer"}, "message": {"type": "string"}}}, "pay.QueryRefundStatusRequest": {"type": "object", "properties": {"head_base": {"description": "请求头信息", "allOf": [{"$ref": "#/definitions/pay.ScanHeadBase"}]}, "mer_order_no": {"description": "商户订单号(原交易)", "type": "string"}, "mer_refund_order_no": {"description": "商户退款单号", "type": "string"}, "trancde": {"description": "交易码,非银联：PF2 银联：QRY3 说明：PF2 为被扫交易类型，支持微信、支付宝、云闪付、电银支付等类型； QRY3 仅支持云闪付（老接口兼容性保留值）", "type": "string"}}}, "pay.QueryRefundStatusResponse": {"type": "object", "properties": {"actual_refund_amount": {"description": "实际退款金额 单位：分", "type": "string"}, "code": {"description": "返回码", "type": "integer"}, "mer_order_no": {"description": "商户订单号", "type": "string"}, "mer_refund_order_no": {"description": "商户退款单号", "type": "string"}, "message": {"type": "string"}, "refund_amount": {"description": "退款金额 单位：分", "type": "string"}, "refund_no": {"description": "退款流水号", "type": "string"}, "refund_result": {"description": "支付结果 S：成功 R：正在执行 F：失败", "type": "string"}, "refund_time": {"description": "退款时间", "type": "string"}}}, "pay.QueryStsResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "dyMchNo": {"description": "电银商户号", "type": "string"}, "dyTermNo": {"description": "电银终端号", "type": "string"}, "message": {"type": "string"}, "status": {"description": "电银状态0正常 1关闭", "type": "string"}}}, "pay.ScanHeadBase": {"type": "object", "properties": {"merc_id": {"description": "商户号", "type": "string"}, "org_id": {"description": "机构号", "type": "string"}, "trm_id": {"description": "终端号，传标准终端绑定接口返回的dyTermNo", "type": "string"}, "trm_sn": {"description": "传机具编号（tsn）", "type": "string"}}}, "pay.SetPwdRequest": {"type": "object", "properties": {"mobile": {"description": "手机号", "type": "string"}, "password": {"description": "密码（MD5）", "type": "string"}, "scrm_id": {"description": "scrm_id", "type": "string"}}}, "pay.StandardPayRefundResponse": {"type": "object", "properties": {"refund_amount": {"description": "退款金额 单位：分", "type": "integer"}, "refund_trade_no": {"description": "支付中心退款流水号", "type": "string"}, "result": {"description": "结果 0：未退款 1：退款成功 2：退款处理中 3：退款失败", "type": "string"}, "result_msg": {"description": "退款返回信息", "type": "string"}, "third_refund_no": {"description": "第三方退款流水号", "type": "string"}, "trade_no": {"description": "支付中心流水号", "type": "string"}}}, "pay.StandardPayResponse": {"type": "object", "properties": {"details": {"description": "统一支付下单返回参数", "allOf": [{"$ref": "#/definitions/pay.UnifiedOrderResponseData"}]}, "order_no": {"description": "商户订单号", "type": "string"}, "pay_amount": {"description": "支付金额 单位：分", "type": "integer"}, "pay_time": {"description": "支付时间", "type": "string"}, "result": {"description": "结果 0： 未支付 1：成功 2：正在执行 3：失败", "type": "string"}, "third_pay_no": {"description": "第三方支付流水号", "type": "string"}, "trade_no": {"description": "支付中心订单号", "type": "string"}, "trans_type": {"description": "支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）", "type": "integer"}}}, "pay.StandardPayStatusResponse": {"type": "object", "properties": {"order_no": {"description": "业务订单号", "type": "string"}, "pay_amount": {"description": "支付金额 单位：分", "type": "integer"}, "pay_time": {"description": "支付时间", "type": "string"}, "refund_amount": {"description": "退款金额 单位：分", "type": "integer"}, "refund_time": {"description": "退款时间", "type": "string"}, "status": {"description": "支付状态 ： 0、未支付，1、已支付，2、部分退款，3、全部退款， 4、处理中， 5、支付失败，6、交易关闭\n退款退款 ： 0、未退款，1、退款成功，2、退款处理中，3、退款失败", "type": "string"}, "third_pay_no": {"description": "第三方支付流水号", "type": "string"}, "third_refund_no": {"description": "第三方退款流水号", "type": "string"}, "trade_no": {"description": "支付中心订单号", "type": "string"}}}, "pay.StdBindingRequest": {"type": "object", "properties": {"dyMchNo": {"description": "电银商户号（需要电银提供）", "type": "string"}, "dyTermNo": {"description": "终端号（自定义） 以绑定结果返回的终端号为准", "type": "string"}, "orgNumber": {"description": "机构号（需要电银提供）", "type": "string"}, "payType": {"description": "支付方式", "type": "integer"}, "shop_id": {"description": "*必传，门店财务编码", "type": "string"}, "snSource": {"description": "机具来源\n1 – 外部代理商(默认)\n2 – 电银代理商\n（当机具来源为“电银代理商”时, 外部终\n端号、终端厂家、终端型号非必传）", "type": "string"}, "termAddress": {"description": "*必传，终端地址（门店地址）", "type": "string"}, "termFactory": {"description": "机具厂商编号（需要电银提供）", "type": "string"}, "termModel": {"description": "机具型号编号（需要电银提供）", "type": "string"}, "termName": {"description": "*必传，终端名称（门店名称）", "type": "string"}, "termType": {"description": "终端类型，默认15", "type": "string"}, "tsn": {"description": "*必传，机具编号(自定义)，新规规则，需要带前缀90000011，示例：90000011RP0045", "type": "string"}}}, "pay.StdBindingResponse": {"type": "object", "properties": {"code": {"description": "返回码", "type": "integer"}, "dyTermNo": {"description": "终端号", "type": "string"}, "message": {"type": "string"}}}, "pay.UnifiedOrderBdPay": {"type": "object", "properties": {"appKey": {"type": "string"}, "bizInfo": {"$ref": "#/definitions/pay.UnifiedOrderBdPayBizInfo"}, "dealId": {"type": "string"}, "dealTitle": {"type": "string"}, "notifyUrl": {"type": "string"}, "rsaSign": {"type": "string"}, "signFieldsRange": {"type": "string"}, "totalAmount": {"type": "string"}, "tpOrderId": {"type": "string"}}}, "pay.UnifiedOrderBdPayBizInfo": {"type": "object", "properties": {"tpData": {"$ref": "#/definitions/pay.BizInfoTpData"}}}, "pay.UnifiedOrderResponseData": {"type": "object", "properties": {"aliTradeNo": {"description": "支付宝小程序交易单号", "type": "string"}, "bank": {"description": "当支付方式是网银", "type": "string"}, "bd_order_info": {"description": "百度支付订单信息", "allOf": [{"$ref": "#/definitions/pay.UnifiedOrderBdPay"}]}, "order_id": {"type": "string"}, "wx_js_app": {"description": "当支付方式为WX_JSAPP返回", "allOf": [{"$ref": "#/definitions/pay.UnifiedOrderWxJsApp"}]}, "wx_jsapi": {"description": "当支付方式为wx_jsapi返回", "allOf": [{"$ref": "#/definitions/pay.UnifiedOrderReturnData"}]}, "wx_native": {"description": "当支付方式为WX_NATIVE,AL_NATIVE返回", "allOf": [{"$ref": "#/definitions/pay.UnifiedOrderWxNative"}]}}}, "pay.UnifiedOrderReturnData": {"type": "object", "properties": {"appId": {"description": "微信appid", "type": "string"}, "nonceStr": {"description": "随机字符串，不长于 32位", "type": "string"}, "package": {"description": "订单详情扩展字符串", "type": "string"}, "paySign": {"description": "签名", "type": "string"}, "signType": {"description": "签名方式", "type": "string"}, "timeStamp": {"description": "时间戳", "type": "string"}}}, "pay.UnifiedOrderWxJsApp": {"type": "object", "properties": {"jsAppId": {"description": "transType 为 WX_JSAPP/AL_JSAPP 时返回", "type": "string"}, "jsAppUrl": {"description": "transType 为 WX_JSAPP/AL_JSAPP 时返回", "type": "string"}}}, "pay.UnifiedOrderWxNative": {"type": "object", "properties": {"payUrl": {"description": "支付链接", "type": "string"}}}, "pay.UpdateByMobileRequest": {"type": "object", "properties": {"code": {"description": "短信验证码", "type": "string"}, "mobile": {"description": "手机号", "type": "string"}, "password": {"description": "密码（MD5）", "type": "string"}, "scrm_id": {"description": "scrm_id", "type": "string"}}}, "pay.UpdateByPwdRequest": {"type": "object", "properties": {"mobile": {"description": "手机号", "type": "string"}, "new_password": {"description": "新密码（MD5）", "type": "string"}, "old_password": {"description": "原密码（MD5）", "type": "string"}, "scrm_id": {"description": "scrm_id", "type": "string"}}}}}