package util

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"strconv"
)

//获取支付的redis信息
func GetPayRedisConn() *redis.Client {
	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	redisClient := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
		//MinIdleConns: 6000,
		//MinIdleConns: 28,
		//IdleTimeout:  30,
		//PoolSize:     512,
		//MaxConnAge:   30 * time.Second,
	})
	_, err := redisClient.Ping().Result()
	if err != nil {
		fmt.Println("redis connections:" + addr + ",paw:" + pwd + ",db:" + strconv.Itoa(db))
		panic("redis连接失败")
	}
	return redisClient
}
