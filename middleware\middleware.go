package middleware

import (
	"pay-center/dto"
	"pay-center/service"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

//校验渠道id和来源，并写入context
func CheckChannel() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if len(c.Request().Header.Get("Authorization")) > 0 {
				jwtToken := c.Request().Header.Get("Authorization")

				if len(jwtToken) <= 0 {
					glog.Error("查看用户的数据的登录信息：", jwtToken)
					return service.ReturnJson(c, 4001, "valid token required.")
					//return r.NewHTTPError(http.StatusUnauthorized, "valid token required.")
				}

				parseAuth, _ := jwt.Parse(strings.Split(jwtToken, " ")[1], func(*jwt.Token) (interface{}, error) {
					return "", nil
				})
				if parseAuth == nil {
					return service.ReturnJson(c, 4001, "valid token required.")
				}
				//if err!=nil{
				//	return service.ReturnJson(c, 4001, "Token 过期")
				//}
				claim := parseAuth.Claims.(jwt.MapClaims)
				var paramMap map[string]interface{}
				paramMap = make(map[string]interface{})
				for key, val := range claim {
					paramMap[key] = val
				}

				//index := strings.Index(jwtToken, " ")
				//count := strings.Count(jwtToken, "")
				//token := jwtToken[index+1 : count-1]
				//publicKeyString := config.GetString("EnterpriseWechatPublicKey")
				//publicKeyString = "-----BEGIN PUBLIC KEY-----\n" + publicKeyString + "\n" + "-----END PUBLIC KEY-----"
				//publicKey, _ := jwt.ParseRSAPublicKeyFromPEM([]byte(publicKeyString))
				//tokenData, err := util.ValidateToken(token, publicKey)
				//if err != nil {
				//	glog.Error("公钥解析登录信息失败，查看用户的数据的登录信息：", jwtToken, err)
				//	return service.ReturnJson(c, 4001, "Token 过期")
				//	//return r.NewHTTPError(http.StatusUnauthorized, fmt.Sprintf("valid token required.%v", err))
				//}
				//claims, ok := tokenData.Claims.(jwt.MapClaims)
				//if !ok {
				//	glog.Error("登录信息验证失败， 查看用户的数据的登录信息：", jwtToken)
				//	//fmt.Println("ParseHStoken:claims类型转换失败")
				//	return nil
				//}
				memberInfo := dto.MemberInfo{
					Mobile:     cast.ToString(paramMap["mobile"]),
					ScrmUserId: cast.ToString(paramMap["scrmid"]),
				}
				c.Set("member_info", &memberInfo)

			}
			c.Set("channel_id", cast.ToInt32(c.Request().Header.Get("channel_id")))
			c.Set("user_agent", cast.ToInt32(c.Request().Header.Get("user_agent")))
			return next(c)
		}
	}
}
