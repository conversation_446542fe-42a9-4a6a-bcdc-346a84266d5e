package service

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"pay-center/dto"
	"pay-center/proto/pay"
)

//GetCardsBalance
// @Summary 获取储蓄卡余额
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.GetCardsBalanceReq true " "
// @Success 200 {object} pay.GetCardsBalanceRes
// @Failure 400 {object} pay.GetCardsBalanceRes
// @Router  /pay/card/balance [GET]
func GetCardsBalance(ctx echo.Context) error {
	//params := new(pay.GetCardsBalanceReq)
	//if err := ctx.Bind(params); err != nil {
	//	return r.NewHTTPError(400, err.Error())
	//}
	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, 400, "用户不存在")
	}
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.GetCardsBalance(payClient.Ctx, &pay.GetCardsBalanceReq{
		ScrmId: memberInfo.ScrmUserId,
	})
	if err != nil {
		glog.Error("调用GetCardsBalance失败，", err, "，参数：", kit.JsonEncode(memberInfo))
		return ReturnJson(ctx, 400, err.Error(), err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)

}

//GetCards
// @Summary 获取储蓄卡列表
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.GetCardsReq true " "
// @Success 200 {object} pay.GetCardsRes
// @Failure 400 {object} pay.GetCardsRes
// @Router /pay/card/list [GET]
func GetCards(ctx echo.Context) error {
	params := new(pay.GetCardsReq)
	if err := ctx.Bind(params); err != nil {
		return ReturnJson(ctx, 400, err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, 400, "用户不存在")
	}
	params.ScrmId = memberInfo.ScrmUserId
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.GetCards(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)
}

//CardPay
// @Summary 储蓄卡支付
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.CardPayReq true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router  /pay/card/pay [POST]
func CardPay(ctx echo.Context) error {
	params := new(pay.CardPayReq)
	if err := ctx.Bind(params); err != nil {
		return ReturnJson(ctx, 400, err.Error())
	}
	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, 400, "用户不存在")
	}
	params.ScrmId = memberInfo.ScrmUserId
	params.Mobile = memberInfo.Mobile
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.CardPay(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	if payRes.Code != 200 {
		return ctx.JSON(400, payRes)
	}
	return ctx.JSON(int(payRes.Code), payRes)
}

//CardRefund
// @Summary 储蓄卡退款
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.CardsRefundReq true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router  /pay/card/refund [POST]
func CardRefund(ctx echo.Context) error {
	params := new(pay.CardsRefundReq)
	if err := ctx.Bind(params); err != nil {
		return ReturnJson(ctx, 400, err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, 400, "用户不存在")
	}
	params.ScrmId = memberInfo.ScrmUserId
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.CardRefund(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)
}

//CardOrderRecord
// @Summary 储蓄卡订单交易记录
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.CardOrderRecordsReq true " "
// @Success 200 {object} pay.CardOrderRecordsRes
// @Failure 400 {object} pay.CardOrderRecordsRes
// @Router  /pay/card/order-record [POST]
func CardOrderRecord(ctx echo.Context) error {

	params := new(pay.CardOrderRecordsReq)
	if err := ctx.Bind(params); err != nil {
		return ReturnJson(ctx, 400, err.Error())
	}
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.CardOrderRecords(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)
}

//CardRecord
// @Summary 储蓄卡交易流水明细
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.CardRecordsReq true " "
// @Success 200 {object} pay.CardRecordsRes
// @Failure 400 {object} pay.CardRecordsRes
// @Router  /pay/card/record [POST]
func CardRecord(ctx echo.Context) error {
	params := new(pay.CardRecordsReq)
	if err := ctx.Bind(params); err != nil {
		return ReturnJson(ctx, 400, err.Error())
	}
	payClient := pay.GetPayCenterClient()
	payRes, err := payClient.RPC.CardRecords(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)
}

//CheckPayType
// @Summary 获取支付方式（默认储蓄卡支付）
// @Tags 储值卡
// @Accept json
// @Produce json
// @Param model body pay.CheckPayTypeReq true " "
// @Success 200 {object} pay.CheckPayTypeRes
// @Failure 400 {object} pay.CheckPayTypeRes
// @Router  /pay/card/pay-type [GET]
func CheckPayType(ctx echo.Context) error {
	params := new(pay.CheckPayTypeReq)
	if err := ctx.Bind(params); err != nil {
		//return r.NewHTTPError(400, err.Error())
		return ReturnJson(ctx, 400, err.Error())
	}

	memberInfo, ok := ctx.Get("member_info").(*dto.MemberInfo)
	if !ok || memberInfo == nil {
		return ReturnJson(ctx, 400, "用户不存在")
	}
	params.ScrmId = memberInfo.ScrmUserId
	payClient := pay.GetPayCenterClient()
	glog.Info(payClient.Conn.GetState().String())
	payRes, err := payClient.RPC.CheckPayType(payClient.Ctx, params)
	if err != nil {
		glog.Error("调用GetCards失败，", err, "，参数：", kit.JsonEncode(params))
		return ReturnJson(ctx, 400, err.Error())
	}
	return ctx.JSON(int(payRes.Code), payRes)
}
