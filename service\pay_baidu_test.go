package service

import (
	"encoding/json"
	"fmt"
	"pay-center/util"
	"testing"
)

func TestBaiduCallbackPay(t *testing.T) {
	var list map[string]string
	jsonStr := `{"count":"1","dealId":"637715928","giftCardMoney":"0","hbBalanceMoney":"0","hbMoney":"0","orderId":"92220561647547","partnerId":"6000001","payMoney":"4","payTime":"1660189654","payType":"1124","promoDetail":"","promoMoney":"0","returnData":"","rsaSign":"XL+ta5z3Vv8KajZY9d/oEJM5TSJPsxCTvDm0cyKJQzuXRcVkgv4m8e/4k36VDwchwmmI3PQn0QL57jZQrdRcvuuDr/pXew0/6XLTbVYBSYulMlxZUiav8P00OKm6B+WxoSl7WuktFkCD8W0aZR1FgoVoZVKyHnLmNCrZG6vZ70w=","status":"2","totalMoney":"4","tpOrderId":"20220811114617591250967598791474","unitPrice":"4","userId":"2302502261"}`
	json.Unmarshal([]byte(jsonStr), &list)

	signData := make(map[string][]string)
	for k, v := range list {
		if k == "rsaSign" {
			continue
		}
		signData[k] = []string{v}
	}

	fmt.Println("params：", util.EncodeForBaidu(signData))
	err := util.VerifySignForBaiduPay(util.EncodeForBaidu(signData), list["rsaSign"])
	fmt.Println(err.Error())
}
